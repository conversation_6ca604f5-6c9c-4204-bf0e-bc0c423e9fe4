<?php
session_start();
include_once 'db_connect.php';
// Check if index exists before accessing
$lang = isset($_GET['_lang']) ? $_GET['_lang'] : 'default_language'; // Provide a default value if the index doesn't exist
$nomaintpl = isset($_GET['nomaintpl']) ? $_GET['nomaintpl'] : ''; // Default to an empty string if not set


$object = new admindashboard();  // Making object of crud class

// Check the action from the AJAX request
if (isset($_POST["action"])) {
    // Load data
    if ($_POST["action"] == "load_data") {
        echo $object->get_data();
    } else if ($_POST["action"] == "usage") {
        echo $object->usage();
    } else if ($_POST["action"] == "serverstats") {
        echo $object->serverstats();
    } else if ($_POST["action"] == "offline_users") {
        echo $object->offline_users();
    } else if ($_POST["action"] == "list_users") {
        echo $object->list_users();
    } else if ($_POST["action"] == "online_users") {
        echo $object->online_users();
    } else if ($_POST["action"] == "expired_users") {
        echo $object->expired_users();
    } else if ($_POST["action"] == "upcoming_expired") {
        echo $object->upcoming_expired();
    } else if ($_POST["action"] == "load_backup_files") {
        echo $object->get_backup_files();
    } else if ($_POST["action"] == "radius_restore_from_file") {
        if (isset($_POST['filename'])) {
            $filename = $_POST['filename'];
            $backupFile = '/var/backup/' . $filename;
            echo $object->radius_restore($backupFile);
        }
    } else if ($_POST["action"] == "radius_backup") {
        echo $object->radius_backup();
    } elseif ($_POST["action"] == "set_schedule" && isset($_POST["schedule"])) {
        $schedule = $_POST["schedule"];
        $time = isset($_POST["time"]) && preg_match('/^\d{2}:\d{2}$/', $_POST["time"]) ? $_POST["time"] : '00:00';

        $localBackup = isset($_POST["local"]) ? filter_var($_POST["local"], FILTER_VALIDATE_BOOLEAN) : true;
        $googleBackup = isset($_POST["google"]) ? filter_var($_POST["google"], FILTER_VALIDATE_BOOLEAN) : true;


        // Debugging: Log the values of local and google to check if they are being passed correctly
        error_log("Local Backup: " . ($localBackup ? 'true' : 'false'));
        error_log("Google Backup: " . ($googleBackup ? 'true' : 'false'));

        // Call the set_backup_schedule function with the extracted values
        $result = $object->set_backup_schedule($schedule, $time, $localBackup, $googleBackup);

        // If needed, you can return the result here
        echo $result;


        // Output success message and current active schedule
        echo "$result\n"; // Output success message
        echo $object->get_active_schedule(); // Output current active schedule
    } elseif ($_POST["action"] == "get_active_schedule") {
        // Get current active schedule
        echo $object->get_active_schedule(); // Output current active schedule directly
    }
}



class admindashboard
{
   public $query;
   function set_backup_schedule($schedule, $time = '00:00', $localBackup = false, $googleBackup = false) {
    $scheduleFile = '/var/backup/backup_schedule.txt';
    $activeScheduleFile = '/var/backup/active_schedule.txt';
    $scriptFile = '/var/backup/backup_script.php';
    $logFile = '/var/backup/backup_setup.log';
    $bashCommand = '';

    // Validate and set time
    if (!preg_match('/^\d{2}:\d{2}$/', $time)) {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - Invalid time provided: $time\n", FILE_APPEND);
        $time = '00:00'; // Default time if invalid
    }
    list($hour, $minute) = explode(':', $time);

    // Define cron job based on schedule
    switch (strtolower($schedule)) {
        case 'daily':
            $bashCommand = "$minute $hour * * * /usr/bin/php $scriptFile daily " . ($localBackup ? 'true' : 'false') . " " . ($googleBackup ? 'true' : 'false') . " >> /var/backup/cron_output.log 2>&1";
            break;
        case 'weekly':
            $bashCommand = "$minute $hour * * 0 /usr/bin/php $scriptFile weekly " . ($localBackup ? 'true' : 'false') . " " . ($googleBackup ? 'true' : 'false') . " >> /var/backup/cron_output.log 2>&1";
            break;
        case 'monthly':
            $bashCommand = "$minute $hour 1 * * /usr/bin/php $scriptFile monthly " . ($localBackup ? 'true' : 'false') . " " . ($googleBackup ? 'true' : 'false') . " >> /var/backup/cron_output.log 2>&1";
            break;
        default:
            file_put_contents($logFile, date('Y-m-d H:i:s') . " - Invalid schedule: $schedule\n", FILE_APPEND);
            return "Error: Invalid schedule. Please use 'daily', 'weekly', or 'monthly'.";
    }

    // Write the backup script
    $scriptContent = <<<PHP
<?php
if (\$argc < 4) {
    die("Error: Missing parameters.\n");
}

\$scheduleType = \$argv[1];
\$localBackup = filter_var(\$argv[2], FILTER_VALIDATE_BOOLEAN);
\$googleBackup = filter_var(\$argv[3], FILTER_VALIDATE_BOOLEAN);

if (!\$localBackup && !\$googleBackup) {
    die("Error: Both local and Google Drive backups are disabled.\n");
}

\$logFile = '/var/backup/backup_log.txt';
\$backupDir = '/var/backup/';
\$database = 'radius';
\$username = 'radius';
\$password = 'radius123';
\$backupFile = \$backupDir . 'radius_backup_' . date('Ymd_His') . '.sql';

// Ensure backup directory exists
if (!is_dir(\$backupDir)) {
    mkdir(\$backupDir, 0755, true);
}

// Backup database
\$command = "mysqldump -u\$username -p\$password \$database > \"\$backupFile\"";
exec(\$command, \$output, \$returnVar);

if (\$returnVar === 0) {
    file_put_contents(\$logFile, date('Y-m-d H:i:s') . " - Backup completed: \$backupFile\n", FILE_APPEND);
    echo "Backup completed: \$backupFile\n";

    if (\$googleBackup) {
        upload_to_google_drive(\$backupFile);
    }
    delete_old_backups();
} else {
    file_put_contents(\$logFile, date('Y-m-d H:i:s') . " - Backup failed. Command output: " . implode("\n", \$output) . "\n", FILE_APPEND);
    echo "Backup failed.\n";
}

function upload_to_google_drive(\$filePath) {
    \$remoteName = 'mybackup'; // RClone remote name
    \$remotePath = "/backups/";

    \$rclonePath = '/usr/bin/rclone';

    \$command = "\${rclonePath} copy \"\$filePath\" \"\${remoteName}:\$remotePath\" --ignore-times";
    exec(\$command . " 2>&1", \$output, \$returnVar);

    file_put_contents('/var/backup/rclone_output.log', "Return code: {\$returnVar}\nOutput: " . implode("\n", \$output) . "\n", FILE_APPEND);

    if (\$returnVar === 0) {
        echo "File uploaded successfully to Google Drive.\n";
    } else {
        echo "Error uploading to Google Drive: " . implode("\n", \$output) . "\n";
    }
}

function delete_old_backups() {
    \$remoteName = 'mybackup'; // RClone remote name
    \$remotePath = "/backups/";
    \$rclonePath = '/usr/bin/rclone';

    // Delete files older than 7 days
    \$command = "\${rclonePath} delete --min-age 7d \${remoteName}:\${remotePath}";
    exec(\$command . " 2>&1", \$output, \$returnVar);
    file_put_contents('/var/backup/rclone_delete.log', "Return code: {\$returnVar}\nOutput: " . implode("\n", \$output) . "\n", FILE_APPEND);

    if (\$returnVar === 0) {
        echo "Old backups deleted successfully.\n";
    } else {
        echo "Error deleting old backups: " . implode("\n", \$output) . "\n";
    }
}
PHP;

    if (file_put_contents($scriptFile, $scriptContent) === false) {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - Failed to write backup script.\n", FILE_APPEND);
        return "Error: Unable to create backup script.";
    }

    // Update crontab
    exec('sudo crontab -l 2>/dev/null', $existingCrontab);
    $existingCrontab = array_filter($existingCrontab, function ($line) use ($scriptFile) {
        return strpos($line, basename($scriptFile)) === false;
    });

    // Add new cron job
    $existingCrontab[] = $bashCommand;

    // Write updated crontab back to system
    file_put_contents('/tmp/crontab.txt', implode("\n", array_unique($existingCrontab)) . "\n");
    exec("sudo crontab /tmp/crontab.txt");

    unlink('/tmp/crontab.txt');

    // Save schedule and options
    if (file_put_contents($activeScheduleFile, "{$schedule}|{$time}|{$localBackup}|{$googleBackup}") === false) {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - Failed to write active schedule.\n", FILE_APPEND);
        return "Error: Unable to save active schedule.";
    }

    return "Backup schedule set successfully for: {$schedule} at {$time}.";
}

public function get_active_schedule() {
    $activeScheduleFile = '/var/backup/active_schedule.txt';
    if (file_exists($activeScheduleFile)) {
        $data = trim(file_get_contents($activeScheduleFile));
        if (strpos($data, '|') !== false) {
            list($schedule, $time, $localBackup, $googleBackup) = explode('|', $data);

            // Convert time to AM/PM format
            list($hour, $minute) = explode(':', $time);
            $amPmTime = date("h:i A", strtotime("$hour:$minute"));
            $localStatus = filter_var($localBackup, FILTER_VALIDATE_BOOLEAN) ? "Enabled" : "Disabled";
            $googleStatus = filter_var($googleBackup, FILTER_VALIDATE_BOOLEAN) ? "Enabled" : "Disabled";
            return "{$schedule} at {$amPmTime}, Local Backup: {$localStatus}, Google Drive Backup: {$googleStatus}";
        }
    }
    return 'No active schedule set.';
}


   function radius_backup()
    {
        $host = 'localhost'; // Database host
        $username = 'radius'; // Database username
        $password = 'radius123'; // Database password
        $databaseName = 'radius'; // Database name for radius
        $outputFile = '/var/backup/backup_radius_' . date('Ymd_His') . '.sql';

        // Command to execute mysqldump with error redirection to output
        $backupCommand = "/usr/bin/mysqldump -h $host -u $username -p$password $databaseName > $outputFile 2>&1";

        // Execute the command and capture the output and return code
        exec($backupCommand, $output, $returnCode);

        if ($returnCode === 0) {
            // Success
            return "Backup created successfully: $outputFile";
        } else {
            // Log errors to server error log
            error_log("mysqldump error: " . implode("\n", $output));

            // Return error message for frontend
            return "Error creating backup. Return code: $returnCode. Details: " . implode("\n", $output);
        }
    }



 // Method to restore from backup
 public function radius_restore($backupFile)
 {
     $host = 'localhost'; // Database host
     $username = 'radius'; // Database username
     $password = 'radius123'; // Database password
     $databaseName = 'radius'; // Database name for radius

     // Validate if the file exists
     if (!file_exists($backupFile)) {
         return "Backup file does not exist: $backupFile";
     }

     // Command to execute MySQL restore (using the backup file)
     $restoreCommand = "/usr/bin/mysql -h $host -u $username -p$password $databaseName < $backupFile 2>&1";

     // Execute the command and capture the output and return code
     exec($restoreCommand, $output, $returnCode);

     if ($returnCode === 0) {
         // Success
         return "Restore completed successfully using file: $backupFile";
     } else {
         // Log errors to server error log
         error_log("mysql restore error: " . implode("\n", $output));

         // Return error message for frontend
         return "Error restoring backup. Return code: $returnCode. Details: " . implode("\n", $output);
     }
 }





   public function online_users() {
        $db = new Database();
        date_default_timezone_set("Asia/Karachi");

        $managerName = isset($_POST['manager_name']) ? $db->conn->real_escape_string($_POST['manager_name']) : null;
        $isAdmin = ($managerName === "admin");
        $searchTerm = isset($_POST['search']) ? '%' . $db->conn->real_escape_string($_POST['search']) . '%' : '';

        // Base Query
        $query = "
            SELECT
                ra.username,
                ra.acctstarttime,
                ra.acctstoptime,
                ra.acctsessiontime,
                ra.acctinputoctets,
                ra.acctoutputoctets,
                ra.callingstationid,
                ra.framedipaddress,
                s.srvname,
                u.expiration,
                CASE WHEN u.expiration <= NOW() THEN 1 ELSE 0 END as is_expired
            FROM radacct ra
            LEFT JOIN rm_users u ON ra.username = u.username
            LEFT JOIN rm_services s ON u.srvid = s.srvid
            WHERE ra.acctstoptime IS NULL";

        // Manager filter
        if (!$isAdmin && $managerName) {
            $query .= " AND u.owner = '{$managerName}'";
        }

        // Search filter
        if ($searchTerm) {
            $query .= " AND (ra.username LIKE '{$searchTerm}' OR ra.callingstationid LIKE '{$searchTerm}') ";
        }

        // Order by start time (most recent session first)
        $query .= " ORDER BY ra.acctstarttime DESC, ra.acctstoptime DESC";

        try {
            $result = $db->conn->query($query);
            if (!$result) {
                throw new Exception("Query failed: " . $db->conn->error);
            }

            $data = array();
            if ($result->num_rows > 0) {
                while ($row = $result->fetch_assoc()) {
                    // Calculate elapsed time
                    $startTime = new DateTime($row['acctstarttime']);
                    $now = new DateTime();
                    $interval = $startTime->diff($now);
                    $elapsedTime = $interval->format('%H:%I:%S');

                    // Format data sizes
                    $uploadedData = $this->formatDataSize($row['acctinputoctets']);
                    $downloadedData = $this->formatDataSize($row['acctoutputoctets']);

                    $data[] = array(
                        'username' => $row['username'],
                        'srvname' => $row['srvname'],
                        'acctstarttime' => $row['acctstarttime'],
                        'elapsedTime' => $elapsedTime,
                        'uploadedData' => $uploadedData,
                        'downloadedData' => $downloadedData,
                        'framedipaddress' => $row['framedipaddress'],
                        'callingstationid' => $row['callingstationid'],
                        'expired' => $row['is_expired'] == 1
                    );
                }
            }

            header('Content-Type: application/json');
            echo json_encode(array(
                "draw" => isset($_POST['draw']) ? intval($_POST['draw']) : 1,
                "recordsTotal" => count($data),
                "recordsFiltered" => count($data),
                "data" => $data
            ));
            exit;

        } catch (Exception $e) {
            error_log("Error fetching online users: " . $e->getMessage());
            header('Content-Type: application/json');
            echo json_encode(array(
                "draw" => isset($_POST['draw']) ? intval($_POST['draw']) : 1,
                "recordsTotal" => 0,
                "recordsFiltered" => 0,
                "data" => array(),
                "error" => "Error loading data. Please contact support."
            ));
            exit;
        }
    }

/**
 * Format data size in human-readable format.
 */
private function formatDataSize($bytes) {
    if ($bytes >= 1073741824) {
        return sprintf("%.2f GB", $bytes / 1073741824);
    } elseif ($bytes >= 1048576) {
        return sprintf("%.2f MB", $bytes / 1048576);
    } elseif ($bytes >= 1024) {
        return sprintf("%.2f KB", $bytes / 1024);
    } else {
        return "{$bytes} Bytes";
    }
}




public function list_users() {
    // Start output buffering to catch any unwanted output
    ob_start();
    
    // Set the content type header
    header('Content-Type: application/json');
    
    $db = new Database();
    date_default_timezone_set("Asia/Karachi");

    try {
        // Query to fetch all users with their status
        $managerName = isset($_POST['manager_name']) ? $db->conn->real_escape_string($_POST['manager_name']) : null;
        $isAdmin = ($managerName === "admin");
        $searchTerm = isset($_POST['search']) ? '%' . $db->conn->real_escape_string($_POST['search']) . '%' : '';
        
        $query = "SELECT 
            u.username, 
            u.firstname, 
            u.lastname, 
            u.address,
            DATE_FORMAT(u.expiration, '%Y-%m-%d') AS expiration,
            DATEDIFF(u.expiration, NOW()) AS days_remaining,
            u.enableuser,
            s.srvname,
            CASE WHEN u.expiration > NOW() THEN 0 ELSE 1 END AS expired
        FROM rm_users u
        LEFT JOIN rm_services s ON u.srvid = s.srvid
        WHERE 1=1";
        
        // Add manager filter if not admin
        if (!$isAdmin && $managerName) {
            $query .= " AND u.manager = '$managerName'";
        }
        
        // Add filters if provided
        if (isset($_POST['enable']) && $_POST['enable'] !== '') {
            if ($_POST['enable'] === '1') {
                $query .= " AND u.enableuser = 1";
            } elseif ($_POST['enable'] === '2') {
                $query .= " AND u.enableuser = 0";
            }
        }
        
        if (isset($_POST['expire']) && $_POST['expire'] !== '') {
            if ($_POST['expire'] === '1') { // Active
                $query .= " AND u.expiration > NOW()";
            } elseif ($_POST['expire'] === '2') { // Expired
                $query .= " AND u.expiration <= NOW()";
            }
        }
        
        if (isset($_POST['package']) && $_POST['package'] !== '') {
            $package = $db->conn->real_escape_string($_POST['package']);
            $query .= " AND u.srvid = '$package'";
        }
        
        // Add search filter if provided
        if ($searchTerm) {
            $query .= " AND (u.username LIKE '$searchTerm' OR u.firstname LIKE '$searchTerm' OR u.lastname LIKE '$searchTerm' OR u.address LIKE '$searchTerm')";
        }
        
        // Execute query
        $result = $db->conn->query($query);
        if (!$result) {
            throw new Exception("Query failed: " . $db->conn->error);
        }
        
        $data = array();
        while ($row = $result->fetch_assoc()) {
            $fullname = trim($row['firstname'] . ' ' . $row['lastname']);
            
            $data[] = array(
                'username' => $row['username'],
                'fullname' => $fullname,
                'address' => $row['address'],
                'srvname' => $row['srvname'],
                'expiration' => $row['expiration'],
                'days_remaining' => $row['days_remaining'],
                'enableuser' => $row['enableuser'],
                'expired' => $row['expired'] == 1
            );
        }
        
        // Clear any output that might have been generated
        ob_clean();
        
        // Return JSON response for DataTables
        $response = json_encode(array(
            "draw" => isset($_POST['draw']) ? intval($_POST['draw']) : 1,
            "recordsTotal" => count($data),
            "recordsFiltered" => count($data),
            "data" => $data
        ));
        
        echo $response;
        
    } catch (Exception $e) {
        // Clear any output that might have been generated
        ob_clean();
        
        error_log("Error in list_users: " . $e->getMessage());
        error_log("Stack trace: " . $e->getTraceAsString());
        
        // Return error as JSON
        echo json_encode(array(
            "draw" => isset($_POST['draw']) ? intval($_POST['draw']) : 1,
            "recordsTotal" => 0,
            "recordsFiltered" => 0,
            "data" => array(),
            "error" => "Error loading data. Please contact support.",
            "debug_message" => $e->getMessage()
        ));
    }
    
    // End output buffering and flush
    ob_end_flush();
    exit;
}


public function offline_users()
{
    // Set proper JSON header
    header('Content-Type: application/json');
    // Disable error output to prevent HTML in JSON response
    error_reporting(0);
    
    $db = new Database();
    date_default_timezone_set("Asia/Karachi");
    $managerName = isset($_POST['manager_name']) ? $db->conn->real_escape_string($_POST['manager_name']) : null;
    $isAdmin = ($managerName === "admin");
    
    try {
        // Query to fetch offline users with radcheck info and proper expiration status
        $query = "SELECT 
            u.username, 
            u.firstname, 
            u.lastname, 
            DATE_FORMAT(u.expiration, '%Y-%m-%d') AS expiration,
            DATEDIFF(u.expiration, NOW()) AS days_remaining,
            s.srvname,
            ra.acctstarttime,
            ra.acctstoptime,
            ra.acctsessiontime AS sessiontime,
            ra.acctinputoctets,
            ra.acctoutputoctets,
            u.enableuser,
            rc.value as password,
            CASE WHEN u.expiration > NOW() THEN 1 ELSE 0 END as is_active,
            CASE 
                WHEN u.expiration > NOW() THEN '#2ed8b6'
                ELSE '#FF5370'
            END as status_color
        FROM rm_users u
        LEFT JOIN rm_services s ON u.srvid = s.srvid
        LEFT JOIN radcheck rc ON u.username = rc.username AND rc.attribute = 'User-Password'
        LEFT JOIN (
            SELECT username, MAX(acctstoptime) as latest_stop
            FROM radacct
            WHERE acctstoptime IS NOT NULL
            GROUP BY username
        ) latest ON u.username = latest.username
        LEFT JOIN radacct ra ON u.username = ra.username 
            AND ra.acctstoptime = latest.latest_stop
        WHERE u.username NOT IN (
            SELECT DISTINCT username 
            FROM radacct 
            WHERE acctstoptime IS NULL
            OR acctstoptime >= NOW() - INTERVAL 1 SECOND
        )";
        
        // Add manager filter if not admin
        if (!$isAdmin && $managerName) {
            $query .= " AND u.manager = '$managerName'";
        }
        
        $query .= " ORDER BY ra.acctstoptime DESC";
        
        $result = $db->conn->query($query);
        
        if (!$result) {
            throw new Exception("Database query failed: " . $db->conn->error);
        }
        
        $data = array();
        
        while ($row = $result->fetch_assoc()) {
            // Format session time
            $sessionTime = "N/A";
            if (isset($row['sessiontime']) && $row['sessiontime'] > 0) {
                $seconds = $row['sessiontime'];
                $hours = floor($seconds / 3600);
                $minutes = floor(($seconds % 3600) / 60);
                $secs = $seconds % 60;
                $sessionTime = "$hours Hrs $minutes Mins $secs Secs";
            }
            
            // Create full name
            $fullname = "N/A";
            if (isset($row['firstname']) && isset($row['lastname'])) {
                $fullname = $row['firstname'] . ' ' . $row['lastname'];
            }
            
            $data[] = array(
                'username' => $row['username'],
                'fullname' => $fullname,
                'acctstarttime' => $row['acctstarttime'],
                'acctstoptime' => $row['acctstoptime'],
                'sessiontime' => $sessionTime,
                'acctinputoctets' => $row['acctinputoctets'],
                'acctoutputoctets' => $row['acctoutputoctets'],
                'srvname' => $row['srvname'],
                'expiration' => $row['expiration'],
                'enableuser' => $row['enableuser'],
                'borderColor' => $row['enableuser'] ? '#1dc4e985' : '#d73925', // Green for enabled, Red for disabled
                'radcheck_value' => $row['radcheck_value']
            );
        }
        
        // Return JSON response for DataTables
        echo json_encode(array(
            "draw" => isset($_POST['draw']) ? intval($_POST['draw']) : 1,
            "recordsTotal" => count($data),
            "recordsFiltered" => count($data),
            "data" => $data
        ));
        
    } catch (Exception $e) {
        error_log("Error fetching offline users: " . $e->getMessage());
        
        // Return error as JSON
        echo json_encode(array(
            "draw" => isset($_POST['draw']) ? intval($_POST['draw']) : 1,
            "recordsTotal" => 0,
            "recordsFiltered" => 0,
            "data" => array(),
            "error" => "Error loading data. Please contact support."
        ));
    }
    exit;
}


// Helper function to format time difference
private function formatTimeDiff($seconds)
{
    $days = floor($seconds / 86400);
    $hours = floor(($seconds % 86400) / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $seconds = $seconds % 60;

    $formattedTime = '';

    if ($days > 0) {
        $formattedTime .= $days . ' ' . ($days === 1 ? 'Day' : 'Days') . ' ';
    }
    if ($hours > 0) {
        $formattedTime .= $hours . ' ' . ($hours === 1 ? 'Hrs' : 'Hrs') . ' ';
    }
    if ($minutes > 0) {
        $formattedTime .= $minutes . ' ' . ($minutes === 1 ? 'Mins' : 'Mins') . ' ';
    }
    if ($seconds > 0 || $formattedTime === '') {
        $formattedTime .= $seconds . ' ' . ($seconds === 1 ? 'Secs' : 'Secs');
    }

    return trim($formattedTime);
}

// Helper function to format bytes
private function formatBytes($bytes)
{
    if ($bytes >= 1073741824) return round($bytes / 1073741824, 2) . ' GB';
    if ($bytes >= 1048576) return round($bytes / 1048576, 2) . ' MB';
    if ($bytes >= 1024) return round($bytes / 1024, 2) . ' KB';
    return $bytes . ' B';
}



public function expired_users() {
    $db = new Database();
    date_default_timezone_set("Asia/Karachi");
    $managerName = isset($_POST['manager_name']) ? $db->conn->real_escape_string($_POST['manager_name']) : null;
    $isAdmin = ($managerName === "admin");

    // Default pagination setup
    $limit = isset($_POST['limit']) ? (int)$_POST['limit'] : 100;
    $offset = isset($_POST['offset']) ? (int)$_POST['offset'] : 0;
    $searchTerm = isset($_POST['search']) ? '%' . $db->conn->real_escape_string($_POST['search']) . '%' : '';

    // Query to fetch expired users along with online/offline status
    $query = "SELECT SQL_CALC_FOUND_ROWS DISTINCT
        ra.username,
        u.firstname,
        u.lastname,
        u.address,
        DATE_FORMAT(u.expiration, '%Y-%m-%d') AS expiration,
        DATEDIFF(u.expiration, NOW()) AS days_remaining,
        u.enableuser,
        s.srvname,
        ra.acctstarttime,
        ra.acctstoptime,
        CASE WHEN u.expiration > NOW() THEN 'active' ELSE 'expired' END AS user_status,
        CASE WHEN ra.acctstoptime IS NULL AND ra.username IS NOT NULL THEN 'online' ELSE 'offline' END AS session_status
    FROM rm_users u
    LEFT JOIN rm_services s ON u.srvid = s.srvid
    LEFT JOIN (
        SELECT username, MAX(acctstarttime) AS latest_session
        FROM radacct
        GROUP BY username
    ) ra_sub ON u.username = ra_sub.username
    LEFT JOIN radacct ra ON ra.username = ra_sub.username AND ra.acctstarttime = ra_sub.latest_session
    WHERE u.expiration <= NOW()";  // This ensures only expired users are selected

    // Apply additional filters if necessary
    if (!$isAdmin && $managerName) {
        $query .= " AND u.owner = '{$managerName}'";
    }
    if (isset($_POST['enable']) && $_POST['enable'] !== '') {
        if ($_POST['enable'] === '1') {
            $query .= " AND u.enableuser = 1";
        } elseif ($_POST['enable'] === '2') {
            $query .= " AND u.enableuser = 0";
        }
    }
    if ($searchTerm) {
        $query .= " AND (u.username LIKE '{$searchTerm}' OR u.firstname LIKE '{$searchTerm}' OR u.lastname LIKE '{$searchTerm}' OR u.address LIKE '{$searchTerm}')";
    }

    // Add sorting to show the most recent expired users first
    $query .= " ORDER BY u.expiration DESC";  // Sort expired users by most recent expiration

    // Add pagination if needed
    if ($limit > 0) {
        $query .= " LIMIT $limit OFFSET $offset";
    }

    try {
        // Execute query to fetch expired user data
        $result = $db->conn->query($query);
        if (!$result) {
            throw new Exception("Query failed: " . $db->conn->error);
        }

        // Get total count of expired users
        $countResult = $db->conn->query("SELECT FOUND_ROWS() AS total_users");
        if (!$countResult) {
            throw new Exception("Count query failed: " . $db->conn->error);
        }
        $countRow = $countResult->fetch_assoc();
        $totalUsers = $countRow['total_users'];

        // Handle export request
        if (isset($_POST['export']) && $_POST['export'] === 'true') {
            $this->export_users($result);
            return; // Stop further processing
        }

        $output = '';
        if ($result->num_rows > 0) {
            $counter = $offset + 1;
            while ($row = $result->fetch_assoc()) {
                // Set border color based on user enable status
                $borderColor = $row['enableuser'] ? '#1dc4e985' : '#d73925';
                // Badge to indicate expired status
                $statusBadge = '<span style="color: #d73925; border: 1px solid #d73925; padding: 3px 5px; border-radius: 3px; font-size: x-small;">EXPIRED</span>';
                // Show online dot if the user is online
                $onlineDot = ($row['session_status'] === 'online') ? "<img src='assets2/images/online.gif' width='7px' alt='Online'>" : "";
                // Display expiration date and days remaining
                $expirationText = "<div>{$row['expiration']}</div><div style='font-size: smaller; color: red;'>" . abs($row['days_remaining']) . " Days Ago</div>";

                $output .= "
                <tr>
                    <td style='border-left: 10px solid {$borderColor}; padding: 8px; text-align: center;'>{$counter}</td>
                    <td style='padding: 8px;'>
                        <a href='admin.php?cont=edit_user&username=" . htmlspecialchars($row['username']) . "'>
                            " . htmlspecialchars($row['username']) . "
                        </a>
                        {$onlineDot} <!-- Show online dot if user is online -->
                        <span style='float: right;'>{$statusBadge}</span>
                    </td>
                    <td style='padding: 8px;'>" . htmlspecialchars($row['firstname'] . ' ' . $row['lastname']) . "</td>
                    <td style='padding: 8px;'>" . htmlspecialchars($row['address']) . "</td>
                    <td style='padding: 8px;'>" . htmlspecialchars($row['srvname']) . "</td>
                    <td style='padding: 8px;'>{$expirationText}</td>
                </tr>";
                $counter++;
            }
        } else {
            $output = '<tr><td colspan="6" class="text-center">No expired users found.</td></tr>';
        }

        // Output the total count and pagination details
        $startCount = $offset + 1;
        $endCount = ($limit > 0) ? min($offset + $limit, $totalUsers) : $totalUsers;
        $pageCount = ($limit > 0) ? ceil($totalUsers / $limit) : 1;

        echo $output;
        echo "<script>
                $('#expired_users_list').data('total-count', {$totalUsers});
                $('#startcount').text({$startCount});
                $('#endcount').text({$endCount});
                $('#totalcount').text({$totalUsers});
                $('#allusers').text({$totalUsers});
                updatePagination({$pageCount}, {$offset}, {$limit});
            </script>";
    } catch (Exception $e) {
        error_log("Error fetching users: " . $e->getMessage());
        echo '<tr><td colspan="6" class="text-center">Error loading data. Please contact support.</td></tr>';
    }
}





public function upcoming_expired() {
    $db = new Database();
    date_default_timezone_set("Asia/Karachi");
    $managerName = isset($_POST['manager_name']) ? $db->conn->real_escape_string($_POST['manager_name']) : null;
    $isAdmin = ($managerName === "admin");

    // Default pagination setup
    $limit = isset($_POST['limit']) ? (int)$_POST['limit'] : 100;
    $offset = isset($_POST['offset']) ? (int)$_POST['offset'] : 0;
    $searchTerm = isset($_POST['search']) ? '%' . $db->conn->real_escape_string($_POST['search']) . '%' : '';

    // Query to fetch users whose expiration is within the next 5 days
    $query = "SELECT SQL_CALC_FOUND_ROWS DISTINCT
        ra.username,
        u.firstname,
        u.lastname,
        u.address,
        DATE_FORMAT(u.expiration, '%Y-%m-%d') AS expiration,
        DATEDIFF(u.expiration, NOW()) AS days_remaining,
        u.enableuser,
        s.srvname,
        ra.acctstarttime,
        ra.acctstoptime,
        CASE WHEN u.expiration > NOW() THEN 'active' ELSE 'expired' END AS user_status,
        CASE WHEN ra.acctstoptime IS NULL AND ra.username IS NOT NULL THEN 'online' ELSE 'offline' END AS session_status
    FROM rm_users u
    LEFT JOIN rm_services s ON u.srvid = s.srvid
    LEFT JOIN (
        SELECT username, MAX(acctstarttime) AS latest_session
        FROM radacct
        GROUP BY username
    ) ra_sub ON u.username = ra_sub.username
    LEFT JOIN radacct ra ON ra.username = ra_sub.username AND ra.acctstarttime = ra_sub.latest_session
    WHERE u.expiration BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL 5 DAY)";  // Fetch users expiring within the next 5 days

    // Apply additional filters if necessary
    if (!$isAdmin && $managerName) {
        $query .= " AND u.owner = '{$managerName}'";
    }
    if (isset($_POST['enable']) && $_POST['enable'] !== '') {
        if ($_POST['enable'] === '1') {
            $query .= " AND u.enableuser = 1";
        } elseif ($_POST['enable'] === '2') {
            $query .= " AND u.enableuser = 0";
        }
    }
    if ($searchTerm) {
        $query .= " AND (u.username LIKE '{$searchTerm}' OR u.firstname LIKE '{$searchTerm}' OR u.lastname LIKE '{$searchTerm}' OR u.address LIKE '{$searchTerm}')";
    }

    // Add sorting to show the most recent expiration dates first
    $query .= " ORDER BY u.expiration ASC";  // Sort by expiration date

    // Add pagination if needed
    if ($limit > 0) {
        $query .= " LIMIT $limit OFFSET $offset";
    }

    try {
        // Execute query to fetch upcoming expired user data
        $result = $db->conn->query($query);
        if (!$result) {
            throw new Exception("Query failed: " . $db->conn->error);
        }

        // Get total count of users with upcoming expiration
        $countResult = $db->conn->query("SELECT FOUND_ROWS() AS total_users");
        if (!$countResult) {
            throw new Exception("Count query failed: " . $db->conn->error);
        }
        $countRow = $countResult->fetch_assoc();
        $totalUsers = $countRow['total_users'];

        // Handle export request
        if (isset($_POST['export']) && $_POST['export'] === 'true') {
            $this->export_users($result);
            return; // Stop further processing
        }

        $output = '';
        if ($result->num_rows > 0) {
            $counter = $offset + 1;
            while ($row = $result->fetch_assoc()) {
                // Set border color based on user enable status
                $borderColor = $row['enableuser'] ? '#1dc4e985' : '#d73925';
                // Badge to indicate upcoming expiration status
                $statusBadge = '<span style="color: green; border: 1px solid green; padding: 3px 5px; border-radius: 3px; font-size: x-small;">EXPIRES SOON</span>';
                // Show online dot if the user is online
                $onlineDot = ($row['session_status'] === 'online') ? "<img src='assets2/images/online.gif' width='7px' alt='Online'>" : "";
                // Display expiration date and days remaining
                $expirationText = "<div>{$row['expiration']}</div><div style='font-size: smaller; color: green;'>" . abs($row['days_remaining']) . " Days Left</div>";

                $output .= "
                <tr>
                    <td style='border-left: 10px solid {$borderColor}; padding: 8px; text-align: center;'>{$counter}</td>
                    <td style='padding: 8px;'>
                        <a href='admin.php?cont=edit_user&username=" . htmlspecialchars($row['username']) . "'>
                            " . htmlspecialchars($row['username']) . "
                        </a>
                        {$onlineDot} <!-- Show online dot if user is online -->
                        <span style='float: right;'>{$statusBadge}</span>
                    </td>
                    <td style='padding: 8px;'>" . htmlspecialchars($row['firstname'] . ' ' . $row['lastname']) . "</td>
                    <td style='padding: 8px;'>" . htmlspecialchars($row['address']) . "</td>
                    <td style='padding: 8px;'>" . htmlspecialchars($row['srvname']) . "</td>
                    <td style='padding: 8px;'>{$expirationText}</td>
                </tr>";
                $counter++;
            }
        } else {
            $output = '<tr><td colspan="6" class="text-center">No users found expiring in the next 5 days.</td></tr>';
        }

        // Output the total count and pagination details
        $startCount = $offset + 1;
        $endCount = ($limit > 0) ? min($offset + $limit, $totalUsers) : $totalUsers;
        $pageCount = ($limit > 0) ? ceil($totalUsers / $limit) : 1;

        echo $output;
        echo "<script>
                $('#upcoming_expired_users_list').data('total-count', {$totalUsers});
                $('#startcount').text({$startCount});
                $('#endcount').text({$endCount});
                $('#totalcount').text({$totalUsers});
                $('#allusers').text({$totalUsers});
                updatePagination({$pageCount}, {$offset}, {$limit});
            </script>";
    } catch (Exception $e) {
        error_log("Error fetching users: " . $e->getMessage());
        echo '<tr><td colspan="6" class="text-center">Error loading data. Please contact support.</td></tr>';
    }
}














   public function serverStats()
   {
       // Fetch server uptime
       $uptimeData = @file_get_contents("/proc/uptime");
       $formattedUptime = "Unable to fetch uptime.";
       if ($uptimeData !== false) {
           $uptimeParts = explode(" ", $uptimeData);
           $uptimeSeconds = (int)$uptimeParts[0];

           if ($uptimeSeconds < 3600) { // Less than an hour
               $minutes = floor($uptimeSeconds / 60);
               $seconds = $uptimeSeconds % 60;
               $formattedUptime = "{$minutes} Minutes {$seconds} Seconds";
           } elseif ($uptimeSeconds < 86400) { // Less than a day
               $hours = floor($uptimeSeconds / 3600);
               $minutes = floor(($uptimeSeconds % 3600) / 60);
               $seconds = $uptimeSeconds % 60;
               $formattedUptime = "{$hours} Hours {$minutes} Minutes {$seconds} Seconds";
           } else { // A day or more
               $days = floor($uptimeSeconds / 86400);
               $hours = floor(($uptimeSeconds % 86400) / 3600);
               $minutes = floor((($uptimeSeconds % 86400) % 3600) / 60);
               $seconds = (($uptimeSeconds % 86400) % 3600) % 60;
               $formattedUptime = "{$days} Days {$hours} Hours {$minutes} Minutes {$seconds} Seconds";
           }
       }

       // Fetch CPU info
       $cpuInfo = @file('/proc/cpuinfo');
       $cpuName = "Unknown CPU";
       if ($cpuInfo) {
           foreach ($cpuInfo as $line) {
               if (strpos($line, "model name") === 0) {
                   $cpuName = trim(str_replace("model name\t: ", "", $line));
                   break;
               }
           }
       }

       // Get memory usage (in MB)
       $free = @shell_exec('free -m');
       $totalMem = $usedMem = $freeMem = $memoryUsage = 0;
       if ($free) {
           $lines = explode("\n", trim($free));
           $mem = array_values(array_filter(explode(" ", $lines[1])));
           $totalMem = isset($mem[1]) ? (int)$mem[1] : 0;
           $usedMem = isset($mem[2]) ? (int)$mem[2] : 0;
           $freeMem = isset($mem[3]) ? (int)$mem[3] : 0;
           $memoryUsage = $totalMem > 0 ? round(($usedMem / $totalMem) * 100, 2) : 0;
       }

       // Get disk usage (in GB)
       try {
           // For VMware Linux systems
           $diskPath = '/';
           
           // Get disk information using df command with specific mount point
           $dfOutput = @shell_exec("df -B1G " . escapeshellarg($diskPath) . " 2>/dev/null");
           
           if ($dfOutput) {
               $lines = explode("\n", trim($dfOutput));
               if (count($lines) > 1) {
                   $values = preg_split('/\s+/', trim($lines[1]));
                   if (count($values) >= 4) {
                       // Convert values to GB
                       $totalDiskGB = (float)$values[1];
                       $usedDiskGB = (float)$values[2];
                       $freeDiskGB = (float)$values[3];
                       
                       // Calculate percentage
                       $diskUsage = round(($usedDiskGB / $totalDiskGB) * 100, 2);
                       
                       // Format numbers to 2 decimal places
                       $totalDiskGB = number_format($totalDiskGB, 2);
                       $usedDiskGB = number_format($usedDiskGB, 2);
                       $freeDiskGB = number_format($freeDiskGB, 2);
                   }
               }
           } else {
               // Fallback to PHP's built-in functions
               $totalDiskBytes = @disk_total_space($diskPath);
               $freeDiskBytes = @disk_free_space($diskPath);
               
               if ($totalDiskBytes !== false && $freeDiskBytes !== false) {
                   $usedDiskBytes = $totalDiskBytes - $freeDiskBytes;
                   
                   // Convert to GB
                   $totalDiskGB = $totalDiskBytes / (1024 * 1024 * 1024);
                   $usedDiskGB = $usedDiskBytes / (1024 * 1024 * 1024);
                   $freeDiskGB = $freeDiskBytes / (1024 * 1024 * 1024);
                   
                   // Format numbers to 2 decimal places
                   $totalDiskGB = number_format($totalDiskGB, 2);
                   $usedDiskGB = number_format($usedDiskGB, 2);
                   $freeDiskGB = number_format($freeDiskGB, 2);
                   
                   // Calculate percentage
                   $diskUsage = round(($usedDiskBytes / $totalDiskBytes) * 100, 2);
               } else {
                   $totalDiskGB = $usedDiskGB = $freeDiskGB = $diskUsage = 0;
               }
           }
           
           // Ensure values are not negative and within reasonable limits
           $totalDiskGB = max(0, $totalDiskGB);
           $usedDiskGB = max(0, $usedDiskGB);
           $freeDiskGB = max(0, $freeDiskGB);
           $diskUsage = max(0, min(100, $diskUsage));
           
       } catch (Exception $e) {
           error_log("Disk usage calculation error: " . $e->getMessage());
           $totalDiskGB = $usedDiskGB = $freeDiskGB = $diskUsage = 0;
       }

       // Get CPU load
       $loadAverage = sys_getloadavg();
       $cpuCores = (int)@shell_exec("grep -c ^processor /proc/cpuinfo");
       $cpuUsage = ($cpuCores > 0) ? round(($loadAverage[0] / $cpuCores) * 100, 2) : 0;

       // Get system time and date
       date_default_timezone_set("Asia/Karachi"); // Set to desired time zone
       $currentTime = date("h:i:s A"); // Time in HH:MM:SS AM/PM format
       $currentDate = date("j M Y");  // Date in day, month, year format
       $timeZone = date_default_timezone_get(); // Fetch current time zone
       $systemTimeDate = " $currentTime,  $currentDate, $timeZone";

       // Generate HTML output with updated cards
       $output = '
       <div class="row" style="margin-top: 15px; margin-bottom: 0px;">
           <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
               <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 8px 32px rgba(29, 196, 233, 0.1);">
                   <div class="card-header py-3" style="background: linear-gradient(90deg, #1dc4e9 0%, #2ed8b6 100%); border-radius: 16px 16px 0 0;">
                       <div class="d-flex justify-content-between align-items-center">
                           <h5 class="mb-0 text-white" style="font-weight: 600; font-size: 1.1rem;">
                               <i class="fas fa-server mr-2"></i>Server Information
                           </h5>
                       </div>
                   </div>
                   <div class="card-body p-4">
                       <div class="row g-3">
                           <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                               <div class="card border-0 h-100 server-card" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 8px 32px ' . ($cpuUsage < 50 ? 'rgba(46, 216, 182, 0.15)' : ($cpuUsage < 80 ? 'rgba(255, 183, 77, 0.15)' : 'rgba(255, 83, 112, 0.15)')) . '; transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); transform: translateY(0);">
                                   <div class="card-body p-3">
                                       <div class="d-flex justify-content-between align-items-center mb-2">
                                           <h6 class="card-title mb-0" style="color: #333f54; font-weight: 600; font-size: 0.9rem; transition: all 0.3s ease;">CPU Usage</h6>
                                           <div class="icon-container" style="width: 35px; height: 35px; border-radius: 10px; background: ' . ($cpuUsage < 50 ? 'linear-gradient(135deg, #2ed8b6 0%, #1dc4e9 100%)' : ($cpuUsage < 80 ? 'linear-gradient(135deg, #FFB64D 0%, #FFA726 100%)' : 'linear-gradient(135deg, #FF5370 0%, #FF4081 100%)')) . '; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px ' . ($cpuUsage < 50 ? 'rgba(46, 216, 182, 0.3)' : ($cpuUsage < 80 ? 'rgba(255, 183, 77, 0.3)' : 'rgba(255, 83, 112, 0.3)')) . '; transition: all 0.3s ease;">
                                               <i class="fas fa-microchip" style="color: #fff; font-size: 1rem; transition: all 0.3s ease;"></i>
                                           </div>
                                       </div>
                                       <h3 class="mb-2" style="color: #333f54; font-weight: 700; font-size: 1.4rem; transition: all 0.3s ease;">' . $cpuUsage . '%</h3>
                                       <div class="progress" style="height: 5px; background-color: #e9ecef; border-radius: 3px; overflow: hidden;">
                                           <div class="progress-bar" role="progressbar" style="width: ' . $cpuUsage . '%; background: ' . ($cpuUsage < 50 ? 'linear-gradient(90deg, #2ed8b6 0%, #1dc4e9 100%)' : ($cpuUsage < 80 ? 'linear-gradient(90deg, #FFB64D 0%, #FFA726 100%)' : 'linear-gradient(90deg, #FF5370 0%, #FF4081 100%)')) . '; border-radius: 3px; transition: width 1s ease-in-out;"></div>
                                       </div>
                                       <p class="card-text mt-2 mb-0"><small class="text-muted" style="font-size: 0.75rem; transition: all 0.3s ease;">' . $cpuName . '</small></p>
                                   </div>
                               </div>
                           </div>
                           <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                               <div class="card border-0 h-100 server-card" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 8px 32px ' . ($memoryUsage < 50 ? 'rgba(46, 216, 182, 0.15)' : ($memoryUsage < 80 ? 'rgba(255, 183, 77, 0.15)' : 'rgba(255, 83, 112, 0.15)')) . '; transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); transform: translateY(0);">
                                   <div class="card-body p-3">
                                       <div class="d-flex justify-content-between align-items-center mb-2">
                                           <h6 class="card-title mb-0" style="color: #333f54; font-weight: 600; font-size: 0.9rem; transition: all 0.3s ease;">Memory Usage</h6>
                                           <div class="icon-container" style="width: 35px; height: 35px; border-radius: 10px; background: ' . ($memoryUsage < 50 ? 'linear-gradient(135deg, #2ed8b6 0%, #1dc4e9 100%)' : ($memoryUsage < 80 ? 'linear-gradient(135deg, #FFB64D 0%, #FFA726 100%)' : 'linear-gradient(135deg, #FF5370 0%, #FF4081 100%)')) . '; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px ' . ($memoryUsage < 50 ? 'rgba(46, 216, 182, 0.3)' : ($memoryUsage < 80 ? 'rgba(255, 183, 77, 0.3)' : 'rgba(255, 83, 112, 0.3)')) . '; transition: all 0.3s ease;">
                                               <i class="fas fa-memory" style="color: #fff; font-size: 1rem; transition: all 0.3s ease;"></i>
                                           </div>
                                       </div>
                                       <h3 class="mb-2" style="color: #333f54; font-weight: 700; font-size: 1.4rem; transition: all 0.3s ease;">' . $memoryUsage . '%</h3>
                                       <div class="progress" style="height: 5px; background-color: #e9ecef; border-radius: 3px; overflow: hidden;">
                                           <div class="progress-bar" role="progressbar" style="width: ' . $memoryUsage . '%; background: ' . ($memoryUsage < 50 ? 'linear-gradient(90deg, #2ed8b6 0%, #1dc4e9 100%)' : ($memoryUsage < 80 ? 'linear-gradient(90deg, #FFB64D 0%, #FFA726 100%)' : 'linear-gradient(90deg, #FF5370 0%, #FF4081 100%)')) . '; border-radius: 3px; transition: width 1s ease-in-out;"></div>
                                       </div>
                                       <p class="card-text mt-2 mb-0"><small class="text-muted" style="font-size: 0.75rem; transition: all 0.3s ease;">' . $usedMem . 'MB / ' . $totalMem . 'MB</small></p>
                                   </div>
                               </div>
                           </div>
                           <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                               <div class="card border-0 h-100 server-card" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 8px 32px ' . ($diskUsage < 50 ? 'rgba(46, 216, 182, 0.15)' : ($diskUsage < 80 ? 'rgba(255, 183, 77, 0.15)' : 'rgba(255, 83, 112, 0.15)')) . '; transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); transform: translateY(0);">
                                   <div class="card-body p-3">
                                       <div class="d-flex justify-content-between align-items-center mb-2">
                                           <h6 class="card-title mb-0" style="color: #333f54; font-weight: 600; font-size: 0.9rem; transition: all 0.3s ease;">Disk Usage</h6>
                                           <div class="icon-container" style="width: 35px; height: 35px; border-radius: 10px; background: ' . ($diskUsage < 50 ? 'linear-gradient(135deg, #2ed8b6 0%, #1dc4e9 100%)' : ($diskUsage < 80 ? 'linear-gradient(135deg, #FFB64D 0%, #FFA726 100%)' : 'linear-gradient(135deg, #FF5370 0%, #FF4081 100%)')) . '; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px ' . ($diskUsage < 50 ? 'rgba(46, 216, 182, 0.3)' : ($diskUsage < 80 ? 'rgba(255, 183, 77, 0.3)' : 'rgba(255, 83, 112, 0.3)')) . '; transition: all 0.3s ease;">
                                               <i class="fas fa-hdd" style="color: #fff; font-size: 1rem; transition: all 0.3s ease;"></i>
                                           </div>
                                       </div>
                                       <h3 class="mb-2" style="color: #333f54; font-weight: 700; font-size: 1.4rem; transition: all 0.3s ease;">' . $diskUsage . '%</h3>
                                       <div class="progress" style="height: 5px; background-color: #e9ecef; border-radius: 3px; overflow: hidden;">
                                           <div class="progress-bar" role="progressbar" style="width: ' . $diskUsage . '%; background: ' . ($diskUsage < 50 ? 'linear-gradient(90deg, #2ed8b6 0%, #1dc4e9 100%)' : ($diskUsage < 80 ? 'linear-gradient(90deg, #FFB64D 0%, #FFA726 100%)' : 'linear-gradient(90deg, #FF5370 0%, #FF4081 100%)')) . '; border-radius: 3px; transition: width 1s ease-in-out;"></div>
                                       </div>
                                       <p class="card-text mt-2 mb-0"><small class="text-muted" style="font-size: 0.75rem; transition: all 0.3s ease;">' . $usedDiskGB . ' GB / ' . $totalDiskGB . ' GB</small></p>
                                   </div>
                               </div>
                           </div>
                           <div class="col-xl-3 col-lg-3 col-md-6 col-sm-6 col-12 mb-3">
                               <div class="card border-0 h-100 server-card" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 8px 32px rgba(46, 216, 182, 0.15); transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); transform: translateY(0);">
                                   <div class="card-body p-3">
                                       <div class="d-flex justify-content-between align-items-center mb-2">
                                           <h6 class="card-title mb-0" style="color: #333f54; font-weight: 600; font-size: 0.9rem; transition: all 0.3s ease;">Server Uptime</h6>
                                           <div class="icon-container" style="width: 35px; height: 35px; border-radius: 10px; background: linear-gradient(135deg, #2ed8b6 0%, #1dc4e9 100%); display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 15px rgba(46, 216, 182, 0.3); transition: all 0.3s ease;">
                                               <i class="fas fa-clock" style="color: #fff; font-size: 1rem; transition: all 0.3s ease;"></i>
                                           </div>
                                       </div>
                                       <h3 class="mb-2" style="color: #333f54; font-weight: 700; font-size: 1.4rem; transition: all 0.3s ease;">' . $formattedUptime . '</h3>
                                       <div class="progress" style="height: 5px; background-color: #e9ecef; border-radius: 3px; overflow: hidden;">
                                           <div class="progress-bar" role="progressbar" style="width: 100%; background: linear-gradient(90deg, #2ed8b6 0%, #1dc4e9 100%); border-radius: 3px; transition: width 1s ease-in-out;"></div>
                                       </div>
                                       <p class="card-text mt-2 mb-0"><small class="text-muted" style="font-size: 0.75rem; transition: all 0.3s ease;">Server Running Time</small></p>
                                   </div>
                               </div>
                           </div>
                       </div>
                   </div>
               </div>
           </div>
       </div>';

       echo $output;
   }

   // Helper function to convert human-readable size to GB
   private function convertToGB($size) {
       $size = trim($size);
       $unit = strtolower(substr($size, -1));
       $value = (float)$size;
       
       switch ($unit) {
           case 'g':
               return $value;
           case 'm':
               return $value / 1024;
           case 'k':
               return $value / (1024 * 1024);
           case 't':
               return $value * 1024;
           default:
               return $value / (1024 * 1024 * 1024);
       }
   }








   public function usage()
   {
       $db = new Database();
       $today = date('Y-m-d');
       $week_start = date('Y-m-d', strtotime('last sunday'));
       $week_end = date('Y-m-d');
       $month_start = date('Y-m-01');
       $last_month_start = date('Y-m-01', strtotime('first day of last month'));
       $last_month_end = date('Y-m-t', strtotime('last day of last month'));


       // Check if user is admin
       $isAdmin = ($_POST['manager_name'] === "admin");
       $managerName = $db->conn->real_escape_string($_POST['manager_name']);

       // Function to fetch data for different periods
       $fetchDataForPeriod = function($startDate, $endDate) use ($db, $isAdmin, $managerName) {
           $query = "
               SELECT
                   radacct.username,
                   u.firstname,
                   u.lastname,
                   ROUND(SUM(acctoutputoctets) / (1024 * 1024 * 1024), 2) AS download,
                   ROUND(SUM(acctinputoctets) / (1024 * 1024 * 1024), 2) AS upload,
                   ROUND((SUM(acctoutputoctets) + SUM(acctinputoctets)) / (1024 * 1024 * 1024), 2) AS total
               FROM radacct
               JOIN rm_users u ON radacct.username = u.username
               WHERE DATE(acctstarttime) BETWEEN '$startDate' AND '$endDate'
                 AND (acctstoptime IS NULL OR DATE(acctstoptime) >= '$startDate')";

           if (!$isAdmin) {
               $query .= " AND u.owner = '$managerName'";
           }

           $query .= " GROUP BY radacct.username, u.firstname, u.lastname
                       ORDER BY total DESC
                       LIMIT 5";

           return $db->conn->query($query);
       };

       // Fetch data for different periods
       $todayData = $fetchDataForPeriod($today, $today);
       $thisWeekData = $fetchDataForPeriod($week_start, $week_end);
       $thisMonthData = $fetchDataForPeriod($month_start, $today);
       $lastMonthData = $fetchDataForPeriod($last_month_start, $last_month_end);

       // Function to prepare rows for table
       $prepareRows = function($data) {
           $rows = '';
           $counter = 1;
           if ($data && $data->num_rows > 0) {
               while ($row = $data->fetch_assoc()) {
                   $rows .= "
                       <tr>
                           <td style='padding: 16px; color: #1dc4e9; font-weight: 600;'>{$counter}</td>
                           <td style='padding: 16px;'>
                               <div class='d-flex flex-column'>
                                   <span style='font-weight: 600; color: #333f54;'>{$row['username']}</span>
                                   <span style='font-size: 0.85rem; color: #6c757d;'>{$row['firstname']} {$row['lastname']}</span>
                               </div>
                           </td>
                           <td style='padding: 16px; text-align: right;'>
                               <div class='d-flex align-items-center justify-content-end'>
                                   <i class='feather icon-download mr-2' style='color: #1dc4e9;'></i>
                                   <span style='font-weight: 600; color: #333f54;'>{$row['download']} GB</span>
                               </div>
                           </td>
                           <td style='padding: 16px; text-align: right;'>
                               <div class='d-flex align-items-center justify-content-end'>
                                   <i class='feather icon-upload mr-2' style='color: #2ed8b6;'></i>
                                   <span style='font-weight: 600; color: #333f54;'>{$row['upload']} GB</span>
                               </div>
                           </td>
                           <td style='padding: 16px; text-align: right;'>
                               <div class='d-flex align-items-center justify-content-end'>
                                   <i class='feather icon-activity mr-2' style='color: #FF5370;'></i>
                                   <span style='font-weight: 600; color: #333f54;'>{$row['total']} GB</span>
                               </div>
                           </td>
                       </tr>";
                   $counter++;
               }
           } else {
               $rows = '<tr><td colspan="5" class="text-center py-4" style="color: #6c757d;">No data available for this period.</td></tr>';
           }
           return $rows;
       };

       // Prepare rows for each period
       $todayRows = $prepareRows($todayData);
       $thisWeekRows = $prepareRows($thisWeekData);
       $thisMonthRows = $prepareRows($thisMonthData);
       $lastMonthRows = $prepareRows($lastMonthData);

       // Generate the HTML output
       $output = '
       <div class="card shadow-sm border-0" style="border-radius: 12px;">
           <div class="card-header py-3" style="background: linear-gradient(90deg, #1dc4e9 0%, #2ed8b6 100%); border-radius: 12px 12px 0 0;">
               <div class="d-flex justify-content-between align-items-center">
                   <h5 class="mb-0 text-white" style="font-weight: 600; font-size: 1.15rem;">
                       <i class="feather icon-activity mr-2"></i>Bandwidth Usage
                   </h5>
                   <div class="d-flex">
                       <ul class="nav nav-tabs card-header-tabs" style="border-bottom: none;">
                           <li class="nav-item">
                               <a class="nav-link active text-black font-weight-bold" href="#usage-tab" data-toggle="tab" style="padding: 8px 16px; border-radius: 8px; margin-right: 8px; background: rgba(255, 255, 255, 0.2);">
                                   <i class="feather icon-activity mr-1"></i>Today
                               </a>
                           </li>
                           <li class="nav-item">
                               <a class="nav-link text-white" href="#this-week-tab" data-toggle="tab" style="padding: 8px 16px; border-radius: 8px; margin-right: 8px;">
                                   <i class="feather icon-calendar mr-1"></i>This Week
                               </a>
                           </li>
                           <li class="nav-item">
                               <a class="nav-link text-white" href="#this-month-tab" data-toggle="tab" style="padding: 8px 16px; border-radius: 8px; margin-right: 8px;">
                                   <i class="feather icon-bar-chart mr-1"></i>This Month
                               </a>
                           </li>
                           <li class="nav-item">
                               <a class="nav-link text-white" href="#last-month-tab" data-toggle="tab" style="padding: 8px 16px; border-radius: 8px;">
                                   <i class="feather icon-archive mr-1"></i>Last Month
                               </a>
                           </li>
                       </ul>
                   </div>
               </div>
           </div>
           <div class="card-body p-0">
               <div class="tab-content">
                   <!-- Today Tab -->
                   <div class="tab-pane fade show active" id="usage-tab">
                       <div class="table-responsive">
                           <table class="table table-hover align-middle mb-0" style="border-radius: 12px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.02);">
                               <thead style="background: linear-gradient(90deg, #e3f7fa 0%, #f0f9ff 100%);">
                                   <tr>
                                       <th style="padding: 16px; width: 60px; color: #1dc4e9; font-weight: 600; border-bottom: 2px solid #e3f7fa;">#</th>
                                       <th style="padding: 16px; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Username</th>
                                       <th style="padding: 16px; text-align: right; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Download</th>
                                       <th style="padding: 16px; text-align: right; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Upload</th>
                                       <th style="padding: 16px; text-align: right; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Total</th>
                                   </tr>
                               </thead>
                               <tbody style="background: #fff;">
                                   ' . $todayRows . '
                               </tbody>
                           </table>
                       </div>
                   </div>
                   
                   <!-- This Week Tab -->
                   <div class="tab-pane fade" id="this-week-tab">
                       <div class="table-responsive">
                           <table class="table table-hover align-middle mb-0" style="border-radius: 12px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.02);">
                               <thead style="background: linear-gradient(90deg, #e3f7fa 0%, #f0f9ff 100%);">
                                   <tr>
                                       <th style="padding: 16px; width: 60px; color: #1dc4e9; font-weight: 600; border-bottom: 2px solid #e3f7fa;">#</th>
                                       <th style="padding: 16px; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Username</th>
                                       <th style="padding: 16px; text-align: right; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Download</th>
                                       <th style="padding: 16px; text-align: right; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Upload</th>
                                       <th style="padding: 16px; text-align: right; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Total</th>
                                   </tr>
                               </thead>
                               <tbody style="background: #fff;">
                                   ' . $thisWeekRows . '
                               </tbody>
                           </table>
                       </div>
                   </div>
                   
                   <!-- This Month Tab -->
                   <div class="tab-pane fade" id="this-month-tab">
                       <div class="table-responsive">
                           <table class="table table-hover align-middle mb-0" style="border-radius: 12px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.02);">
                               <thead style="background: linear-gradient(90deg, #e3f7fa 0%, #f0f9ff 100%);">
                                   <tr>
                                       <th style="padding: 16px; width: 60px; color: #1dc4e9; font-weight: 600; border-bottom: 2px solid #e3f7fa;">#</th>
                                       <th style="padding: 16px; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Username</th>
                                       <th style="padding: 16px; text-align: right; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Download</th>
                                       <th style="padding: 16px; text-align: right; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Upload</th>
                                       <th style="padding: 16px; text-align: right; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Total</th>
                                   </tr>
                               </thead>
                               <tbody style="background: #fff;">
                                   ' . $thisMonthRows . '
                               </tbody>
                           </table>
                       </div>
                   </div>
                   
                   <!-- Last Month Tab -->
                   <div class="tab-pane fade" id="last-month-tab">
                       <div class="table-responsive">
                           <table class="table table-hover align-middle mb-0" style="border-radius: 12px; overflow: hidden; box-shadow: 0 2px 4px rgba(0,0,0,0.02);">
                               <thead style="background: linear-gradient(90deg, #e3f7fa 0%, #f0f9ff 100%);">
                                   <tr>
                                       <th style="padding: 16px; width: 60px; color: #1dc4e9; font-weight: 600; border-bottom: 2px solid #e3f7fa;">#</th>
                                       <th style="padding: 16px; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Username</th>
                                       <th style="padding: 16px; text-align: right; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Download</th>
                                       <th style="padding: 16px; text-align: right; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Upload</th>
                                       <th style="padding: 16px; text-align: right; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Total</th>
                                   </tr>
                               </thead>
                               <tbody style="background: #fff;">
                                   ' . $lastMonthRows . '
                               </tbody>
                           </table>
                       </div>
                   </div>
               </div>
           </div>
       </div>';

       // Add JavaScript to handle tab switching
       $output .= '
       <script>
       $(document).ready(function() {
           // Function to update tab styles
           function updateTabStyles(activeTab) {
               $(".nav-tabs .nav-link").each(function() {
                   if ($(this).attr("href") === activeTab) {
                       $(this).addClass("active text-black").removeClass("text-white");
                       $(this).css("background", "rgba(255, 255, 255, 0.2)");
                   } else {
                       $(this).removeClass("active text-black").addClass("text-white");
                       $(this).css("background", "transparent");
                   }
               });
           }

           // Handle tab clicks
           $(".nav-tabs .nav-link").on("click", function(e) {
               e.preventDefault();
               var targetTab = $(this).attr("href");
               
               // Update tab styles
               updateTabStyles(targetTab);
               
               // Show the target tab content
               $(".tab-content .tab-pane").removeClass("show active");
               $(targetTab).addClass("show active");

               // Trigger Bootstrap tab event
               $(this).tab("show");
           });

           // Initialize with Today tab active
           updateTabStyles("#usage-tab");
           
           // Handle Bootstrap tab events
           $("a[data-toggle=\'tab\']").on("shown.bs.tab", function (e) {
               var targetTab = $(e.target).attr("href");
               updateTabStyles(targetTab);
           });
       });
       </script>';

       return $output;
   }







      public function get_data()
      {
          $db = new Database();
          $output = '';
          date_default_timezone_set("Asia/Karachi");
          $date = date('Y-m-d');
          $new_date = date('Y-m-d', strtotime($date . ' +7 day'));
          $isAdmin = ($_POST['manager_name'] === "admin");
          $managerName = isset($_POST['manager_name']) ? $db->conn->real_escape_string($_POST['manager_name']) : '';

          // Prepare conditions based on admin or specific manager
          $managerCondition = $isAdmin ? "" : "WHERE owner='" . $managerName . "'";
          $usersCondition = $isAdmin ? "" : "AND owner='" . $managerName . "'";

          // Query results
          $rowtotalusers = $this->fetch_query_result("SELECT IF(COUNT(username) = 0, 0, COUNT(username)) AS result FROM rm_users $managerCondition", $db);
          $rowactiveusers = $this->fetch_query_result("SELECT IF(COUNT(username) = 0, 0, COUNT(username)) AS result FROM rm_users WHERE expiration > '" . date('Y-m-d 00:00:01') . "' $usersCondition", $db);
          $rowexpireusers = $this->fetch_query_result("SELECT IF(COUNT(username) = 0, 0, COUNT(username)) AS result FROM rm_users WHERE expiration <= CURRENT_DATE $usersCondition", $db);
          $rowonlineusers = $this->fetch_query_result("SELECT COUNT(radacct.username) AS result FROM radacct JOIN rm_users ON rm_users.username = radacct.username WHERE radacct.acctstoptime IS NULL $usersCondition", $db);
          $rowofflineusers = $this->fetch_query_result("
    SELECT 
        COUNT(DISTINCT u.username) as total_offline,
        COUNT(DISTINCT CASE WHEN u.expiration > NOW() THEN u.username END) as active_offline,
        COUNT(DISTINCT CASE WHEN u.expiration <= NOW() THEN u.username END) as expired_offline
    FROM rm_users u 
    WHERE NOT EXISTS (
        SELECT 1 
        FROM radacct ra 
        WHERE ra.username = u.username 
        AND (ra.acctstoptime IS NULL OR ra.acctstoptime >= NOW() - INTERVAL 1 SECOND)
    ) $usersCondition", $db);
          $rowactiveonlineusers = $this->fetch_query_result("SELECT IF(COUNT(radacct.username) = 0, 0, COUNT(radacct.username)) AS result FROM radacct JOIN rm_users ON rm_users.username = radacct.username WHERE radacct.acctstoptime IS NULL AND expiration > '" . date('Y-m-d 00:00:01') . "' $usersCondition", $db);
          $rowonlineexpiredusers = $this->fetch_query_result("SELECT IF(COUNT(radacct.username) = 0, 0, COUNT(radacct.username)) AS result FROM radacct JOIN rm_users ON rm_users.username = radacct.username WHERE radacct.acctstoptime IS NULL AND expiration < '" . date('Y-m-d 00:00:01') . "' $usersCondition", $db);
          
          // Corrected wallet balance query
          $rowmanagerbalance = $this->fetch_query_result("SELECT balance AS result FROM rm_managers WHERE managername = '$managerName'", $db);
          
          // New users query (users created in last 30 days)
          $rownewusers = $this->fetch_query_result("SELECT IF(COUNT(username) = 0, 0, COUNT(username)) AS result FROM rm_users WHERE createdon >= DATE_SUB(NOW(), INTERVAL 7 DAY) $usersCondition", $db);
          
          // Disabled users query
          $rowdisableusers = $this->fetch_query_result("SELECT IF(COUNT(username) = 0, 0, COUNT(username)) AS result FROM rm_users WHERE enableuser = 0 $usersCondition", $db);
           
          // Disabled users query
          $rowenableusers = $this->fetch_query_result("SELECT IF(COUNT(username) = 0, 0, COUNT(username)) AS result FROM rm_users WHERE enableuser = 1 $usersCondition", $db);
          // This month's topup query
          $thisMonthTopup = $this->fetch_query_result("SELECT IF(SUM(amount) IS NULL, 0, SUM(amount)) AS result FROM rm_topup WHERE DATE(created_at) >= DATE_FORMAT(NOW(), '%Y-%m-01') AND managername = '$managerName'", $db);
          
          // Last month's topup query
          $lastMonthTopup = $this->fetch_query_result("SELECT IF(SUM(amount) IS NULL, 0, SUM(amount)) AS result FROM rm_topup WHERE DATE(created_at) >= DATE_FORMAT(DATE_SUB(NOW(), INTERVAL 1 MONTH), '%Y-%m-01') AND DATE(created_at) < DATE_FORMAT(NOW(), '%Y-%m-01') AND managername = '$managerName'", $db);
          
          $rowexpiretoday = $this->fetch_query_result("SELECT IF(COUNT(username) = 0, 0, COUNT(username)) AS result FROM rm_users WHERE expiration = CURRENT_DATE $usersCondition", $db);
          $rowexpirethisweek = $this->fetch_query_result("SELECT IF(COUNT(username) = 0, 0, COUNT(username)) AS result FROM rm_users WHERE expiration BETWEEN CURRENT_DATE AND DATE_ADD(CURRENT_DATE, INTERVAL 5 DAY) $usersCondition", $db);

          // Rest of your HTML output code...

          // HTML Output
          $output .= '




          <div class="row" style="margin-top: 0px; margin-left: 0; margin-right: 0; width: 110%;">
              <div class="col-xl-4 col-md-6">
                  <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 8px 32px rgba(29, 196, 233, 0.1);">
                      <div class="card-body p-4">
                          <div class="d-flex align-items-center">
                              <div style="background: linear-gradient(135deg, #1dc4e9 0%, #2ed8b6 100%); border-radius: 16px; width: 64px; height: 64px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 16px rgba(29, 196, 233, 0.2);">
                                  <i class="feather icon-credit-card text-white" style="font-size: 2rem;"></i>
                              </div>
                              <div class="ms-4 text-center flex-grow-1">
                                  <div class="text-muted" style="font-size: 0.95rem; font-weight: 500;">THIS MONTH\'S TOPUP</div>
                                  <div class="fs-4 fw-bold" style="color: #1dc4e9; line-height: 1.4;">PKR ' . number_format($thisMonthTopup->result, 2) . '</div>
                              </div>
                          </div>
                          <div class="progress mt-3" style="height: 6px; background: rgba(29, 196, 233, 0.1);">
                              <div class="progress-bar" style="width: 100%; background: linear-gradient(90deg, #1dc4e9 0%, #2ed8b6 100%);"></div>
                          </div>
                      </div>
                  </div>
              </div>
              <div class="col-xl-4 col-md-6">
                  <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 8px 32px rgba(46, 216, 182, 0.1);">
                      <div class="card-body p-4">
                          <div class="d-flex align-items-center">
                              <div style="background: linear-gradient(135deg, #2ed8b6 0%, #1dc4e9 100%); border-radius: 16px; width: 64px; height: 64px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 16px rgba(46, 216, 182, 0.2);">
                                  <i class="feather icon-credit-card text-white" style="font-size: 2rem;"></i>
                              </div>
                              <div class="ms-4 text-center flex-grow-1">
                                  <div class="text-muted" style="font-size: 0.95rem; font-weight: 500;">LAST MONTH\'S TOPUP</div>
                                  <div class="fs-4 fw-bold" style="color: #2ed8b6; line-height: 1.4;">PKR ' . number_format($lastMonthTopup->result, 2) . '</div>
                              </div>
                          </div>
                          <div class="progress mt-3" style="height: 6px; background: rgba(46, 216, 182, 0.1);">
                              <div class="progress-bar" style="width: 100%; background: linear-gradient(90deg, #2ed8b6 0%, #1dc4e9 100%);"></div>
                          </div>
                      </div>
                  </div>
              </div>
              <div class="col-xl-4 col-md-6">
                  <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 8px 32px rgba(255, 183, 77, 0.1);">
                      <div class="card-body p-4">
                          <div class="d-flex align-items-center">
                              <div style="background: linear-gradient(135deg, #FFB64D 0%, #FF5370 100%); border-radius: 16px; width: 64px; height: 64px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 16px rgba(255, 183, 77, 0.2);">
                                  <i class="feather icon-credit-card text-white" style="font-size: 2rem;"></i>
                              </div>
                              <div class="ms-4 text-center flex-grow-1">
                                  <div class="text-muted" style="font-size: 0.95rem; font-weight: 500;">WALLET BALANCE</div>
                                  <div class="fs-4 fw-bold" style="color: #FFB64D; line-height: 1.4;">PKR ' . number_format($rowmanagerbalance->result, 2) . '</div>
                              </div>
                          </div>
                          <div class="progress mt-3" style="height: 6px; background: rgba(255, 183, 77, 0.1);">
                              <div class="progress-bar" style="width: 100%; background: linear-gradient(90deg, #FFB64D 0%, #FF5370 100%);"></div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>

          <div class="col-md-12">
              <div class="card border-0" style="background: rgba(255,255,255,0.85); backdrop-filter: blur(12px); border-radius: 18px; box-shadow: 0 8px 32px rgba(29,196,233,0.12); margin-bottom: 24px; border-left: 6px solid #1dc4e9;">
                  <div class="card-body d-flex align-items-center p-4">
                      <div style="background: linear-gradient(135deg, #1dc4e9 0%, #2ed8b6 100%); border-radius: 14px; width: 56px; height: 56px; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 12px rgba(29,196,233,0.18);">
                          <i class="feather icon-alert-circle text-white" style="font-size: 2rem;"></i>
                      </div>
                      <div class="ms-4" style="flex:1; margin-left: 24px;">
                          <div class="text-muted" style="font-size: 1.08rem; font-weight: 700; margin-bottom: 6px; letter-spacing: 0.5px;">Important Notice</div>
                          <div style="color: #556978; font-size: 1rem; line-height: 1.7; font-weight: 500;">
                              As per PTA instruction, all dealers are requested to upload scanned CNIC and complete update user details including phone, mobile, address.
                          </div>
                      </div>
                  </div>
              </div>
          </div>

          
  <div class="col-xl-8 col-md-12">
      <div class="card shadow-sm border-0" style="border-radius: 12px;">
          <div class="card-header py-1" style="background: linear-gradient(90deg, #1dc4e9 0%, #2ed8b6 100%); border-radius: 12px 12px 0 0;">
              <div class="d-flex justify-content-between align-items-center">
                  <h5 class="mb-0 text-white" style="font-weight: 600; font-size: 1.15rem;">
                      <i class="feather icon-users mr-2"></i>Customer Statistics
                  </h5>
                  <div class="dropdown">
                      <button class="btn btn-sm btn-light dropdown-toggle" type="button" id="accountDropdown" data-toggle="dropdown">
                          <i class="feather icon-filter mr-1"></i>Filter Account
                      </button>
                      <div class="dropdown-menu dropdown-menu-right">
                          <a class="dropdown-item" href="#">All Accounts</a>
                          <a class="dropdown-item" href="#">Manager 1</a>
                          <a class="dropdown-item" href="#">Manager 2</a>
                      </div>
                  </div>
              </div>
          </div>
          <div class="card-body p-3" style="background: #f8fafd;">
              <!-- Main Stats Row -->
              <div class="row g-3">
                  <!-- Total Customers -->
                  <div class="col-md-6">
                      <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 8px 32px rgba(29, 196, 233, 0.1);">
                          <div class="card-body p-4">
                              <div class="d-flex align-items-center">
                                  <div style="background: linear-gradient(135deg, #1dc4e9 0%, #2ed8b6 100%); border-radius: 16px; width: 64px; height: 64px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 16px rgba(29, 196, 233, 0.2);">
                                      <i class="feather icon-users text-white" style="font-size: 2rem;"></i>
                                  </div>
                                  <div class="ms-4 text-center flex-grow-1">
                                      <div class="fs-1 fw-bold" style="color: #1dc4e9; line-height: 1;">'.$rowtotalusers->result.'</div>
                                      <div class="text-muted" style="font-size: 0.95rem; font-weight: 500;">Total Customers</div>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
                  <!-- Active Customers -->
   <div class="col-md-6">
                         <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 8px 32px rgba(46, 216, 182, 0.1);">
                          <div class="card-body p-4">
                              <div class="d-flex align-items-center">
                                  <div style="background: linear-gradient(135deg, #2ed8b6 0%, #1dc4e9 100%); border-radius: 16px; width: 64px; height: 64px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 16px rgba(46, 216, 182, 0.2);">
                                      <i class="feather icon-user-check text-white" style="font-size: 2rem;"></i>
                                  </div>
                                  <div class="ms-4 text-center flex-grow-1">
                                      <div class="fs-1 fw-bold" style="color: #2ed8b6; line-height: 1;">'.$rowactiveusers->result.'</div>
                                      <div class="text-muted" style="font-size: 0.95rem; font-weight: 500;">Active Customers</div>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
                  <!-- Online Users -->
                  <div class="col-md-12">
                      <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 8px 32px rgba(29, 196, 233, 0.1);">
                          <div class="card-body p-4">
                              <div class="d-flex align-items-center mb-4">
                                  <div style="background: linear-gradient(135deg, #1dc4e9 0%, #2ed8b6 100%); border-radius: 16px; width: 64px; height: 64px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 16px rgba(29, 196, 233, 0.2);">
                                      <i class="feather icon-globe text-white" style="font-size: 2rem;"></i>
                                  </div>
                                  <div class="ms-4 text-center flex-grow-1">
                                      <div class="fs-1 fw-bold" style="color: #1dc4e9; line-height: 1;">'.$rowonlineusers->result.'</div>
                                      <div class="text-muted" style="font-size: 0.95rem; font-weight: 500;">Online Users</div>
                                  </div>
                              </div>
                              <div class="row g-2">
                                  <div class="col">
                                      <div class="p-2 rounded text-center" style="background: rgba(46, 216, 182, 0.1);">
                                          <div class="fw-bold" style="color: #2ed8b6;">'.$rowactiveonlineusers->result.'</div>
                                          <div class="small text-muted">Active</div>
                                      </div>
                                  </div>
                                  <div class="col">
                                      <div class="p-2 rounded text-center" style="background: rgba(255, 83, 112, 0.1);">
                                          <div class="fw-bold" style="color: #FF5370;">'.$rowonlineexpiredusers->result.'</div>
                                          <div class="small text-muted">Expired</div>
                                      </div>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>

       
              <!-- Original Secondary Stats Row -->
              <div class="row g-3 mt-3">
                  <!-- Expired -->
                  <div class="col-md-4">
                      <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 8px 32px rgba(255, 83, 112, 0.1);">
                          <div class="card-body p-4">
                              <div class="d-flex align-items-center">
                                  <div style="background: linear-gradient(135deg, #FF5370 0%, #FFB64D 100%); border-radius: 16px; width: 64px; height: 64px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 16px rgba(255, 83, 112, 0.2);">
                                      <i class="feather icon-alert-circle text-white" style="font-size: 2rem;"></i>
                                  </div>
                                  <div class="ms-4 text-center flex-grow-1">
                                      <div class="fs-1 fw-bold" style="color: #FF5370; line-height: 1;">'.$rowexpireusers->result.'</div>
                                      <div class="text-muted" style="font-size: 0.95rem; font-weight: 500;">Expire Users</div>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
               
                  <!-- Today Expire -->
                  <div class="col-md-4">
                      <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 8px 32px rgba(255, 183, 77, 0.1);">
                          <div class="card-body p-4">
                              <div class="d-flex align-items-center">
                                  <div style="background: linear-gradient(135deg, #FF5370 0%, #FFB64D 100%); border-radius: 16px; width: 64px; height: 64px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 16px rgba(255, 183, 77, 0.2);">
                                      <i class="feather icon-clock text-white" style="font-size: 2rem;"></i>
                                  </div>
                                  <div class="ms-4 text-center flex-grow-1">
                                      <div class="fs-1 fw-bold" style="color: #FFB64D; line-height: 1;">'.$rowexpiretoday->result.'</div>
                                      <div class="text-muted" style="font-size: 0.95rem; font-weight: 500;">Today Expire</div>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
                  <!-- Upcoming Expire -->
                  <div class="col-md-4">
                      <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 8px 32px rgba(46, 216, 182, 0.1);">
                          <div class="card-body p-4">
                              <div class="d-flex align-items-center">
                                  <div style="background: linear-gradient(135deg, #2ed8b6 0%, #1dc4e9 100%); border-radius: 16px; width: 64px; height: 64px; display: flex; align-items: center; justify-content: center; box-shadow: 0 8px 16px rgba(46, 216, 182, 0.2);">
                                      <i class="feather icon-calendar text-white" style="font-size: 2rem;"></i>
                                  </div>
                                  <div class="ms-4 text-center flex-grow-1">
                                      <div class="fs-1 fw-bold" style="color: #2ed8b6; line-height: 1;">'.$rowexpirethisweek->result.'</div>
                                      <div class="text-muted" style="font-size: 0.95rem; font-weight: 500;">Upcoming Expire</div>
                                  </div>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </div>

  <div class="col-xl-4 col-md-6">
      <!-- Online Users Card -->
      <div class="card border-0" style="background: rgba(255,255,255,0.85); backdrop-filter: blur(12px); border-radius: 18px; box-shadow: 0 8px 32px '.($rowonlineusers->result > 0 ? 'rgba(29,196,233,0.12)' : 'rgba(255,83,112,0.12)').'; margin-bottom: 20px;">
          <div class="card-body p-4">
              <div class="d-flex align-items-center justify-content-between mb-4">
                  <div>
                      <h5 class="text-muted mb-1" style="font-weight: 600; font-size: 1.1rem; letter-spacing: 0.5px;">ONLINE USERS</h5>
                      <div class="d-flex align-items-center">
                          <h2 class="mb-0" style="font-size: 2.5rem; color: '.($rowonlineusers->result > 0 ? '#1dc4e9' : '#FF5370').'; font-weight: bold;">'.$rowonlineusers->result.'</h2>
                          <span class="badge ms-2" style="background: '.($rowonlineusers->result > 0 ? 'rgba(29,196,233,0.1)' : 'rgba(255,83,112,0.1)').'; color: '.($rowonlineusers->result > 0 ? '#1dc4e9' : '#FF5370').'; font-size: 0.9rem; padding: 5px 10px;">LIVE</span>
                      </div>
                  </div>
                  <div style="background: '.($rowonlineusers->result > 0 ? 'linear-gradient(135deg, #1dc4e9 0%, #2ed8b6 100%)' : 'linear-gradient(135deg, #FF5370 0%, #FFB64D 100%)').'; border-radius: 16px; width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 12px '.($rowonlineusers->result > 0 ? 'rgba(29,196,233,0.18)' : 'rgba(255,83,112,0.18)').';">
                      <div style="width: 12px; height: 12px; background: white; border-radius: 50%; position: relative;">
                          <div style="position: absolute; width: 100%; height: 100%; background: white; border-radius: 50%; animation: '.($rowonlineusers->result > 0 ? 'ripple 1.5s infinite' : 'ripple-danger 1.5s infinite').';"></div>
                          <div style="position: absolute; width: 100%; height: 100%; background: white; border-radius: 50%; animation: '.($rowonlineusers->result > 0 ? 'ripple 1.5s infinite 0.5s' : 'ripple-danger 1.5s infinite 0.5s').';"></div>
                          <div style="position: absolute; width: 100%; height: 100%; background: white; border-radius: 50%; animation: '.($rowonlineusers->result > 0 ? 'ripple 1.5s infinite 1s' : 'ripple-danger 1.5s infinite 1s').';"></div>
                      </div>
                  </div>
              </div>
              <div class="d-flex justify-content-between align-items-center">
                  <a href="print_current.php?dealer=murad.franchise" target="_blank" class="btn btn-sm" style="background: '.($rowonlineusers->result > 0 ? 'rgba(29,196,233,0.1)' : 'rgba(255,83,112,0.1)').'; color: '.($rowonlineusers->result > 0 ? '#1dc4e9' : '#FF5370').'; border: none; padding: 8px 16px; border-radius: 8px; font-weight: 500;">
                      <i class="feather icon-printer mr-1"></i> Print List
                  </a>
                  <a href="admin.php?cont=online_users" class="btn btn-sm" style="background: '.($rowonlineusers->result > 0 ? 'linear-gradient(135deg, #1dc4e9 0%, #2ed8b6 100%)' : 'linear-gradient(135deg, #FF5370 0%, #FFB64D 100%)').'; color: white; border: none; padding: 8px 16px; border-radius: 8px; font-weight: 500;">
                      <i class="feather icon-list mr-1"></i> View Details
                  </a>
              </div>
          </div>
      </div>

 

   <!-- Offline Users Card -->
      <div class="card border-0" style="background: rgba(255,255,255,0.85); backdrop-filter: blur(12px); border-radius: 18px; box-shadow: 0 8px 32px rgba(255,83,112,0.12);">
          <div class="card-body p-4">
              <div class="d-flex align-items-center justify-content-between mb-4">
                  <div>
                      <h5 class="text-muted mb-1" style="font-weight: 600; font-size: 1.1rem; letter-spacing: 0.5px;">OFFLINE USERS</h5>
                      <div class="d-flex align-items-center">
                          <h2 class="mb-0" style="font-size: 2.5rem; color: #FF5370; font-weight: bold;">'.$rowofflineusers->total_offline.'</h2>
                          <span class="badge ms-2" style="background: rgba(255,83,112,0.1); color: #FF5370; font-size: 0.9rem; padding: 5px 10px;">TOTAL</span>
              </div>
          </div>
                  <div style="background: linear-gradient(135deg, #FF5370 0%, #FFB64D 100%); border-radius: 16px; width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 12px rgba(255,83,112,0.18);">
                      <i class="feather icon-users text-white" style="font-size: 1.8rem;"></i>
      </div>
              </div>
              <div class="d-flex justify-content-between align-items-center">
                  <div class="d-flex align-items-center">
                      <div class="me-3 text-center">
                          <div class="small text-muted mb-1">Active </div>
                          <div style="font-weight: 600; color: #2ed8b6;">'.$rowofflineusers->active_offline.'</div>
                      </div>
                      <span style="margin: 0 15px;">|</span>
                      <div class="me-3 text-center">
                          <div class="small text-muted mb-1"> Expired</div>
                          <div style="font-weight: 600; color: #FF5370;">'.$rowofflineusers->expired_offline.'</div>
              </div>
          </div>
                  <a href="admin.php?cont=offline_users" class="btn btn-sm" style="background: linear-gradient(135deg, #FF5370 0%, #FFB64D 100%); color: white; border: none; padding: 8px 16px; border-radius: 8px; font-weight: 500;">
                      <i class="feather icon-list mr-1"></i> View Details
                  </a>
              </div>
          </div>
      </div>

    <!-- Expired Users Card -->
    <div class="card border-0" style="background: rgba(255,255,255,0.85); backdrop-filter: blur(12px); border-radius: 18px; box-shadow: 0 8px 32px rgba(46,216,182,0.12);">
        <div class="card-body p-4">
            <div class="d-flex align-items-center justify-content-between mb-4">
                <div>
                    <h5 class="text-muted mb-1" style="font-weight: 600; font-size: 1.1rem; letter-spacing: 0.5px;">EXPIRED USERS</h5>
                    <div class="d-flex align-items-center">
                        <h2 class="mb-0" style="font-size: 2.5rem; color: #FF5370; font-weight: bold;">'.$rowexpireusers->result.'</h2>
                        <span class="badge ms-3" style="background: rgba(255,83,112,0.1); color: #FF5370; font-size: 0.9rem; padding: 5px 10px;">TOTAL</span>
                    </div>
                </div>
                <div style="background: linear-gradient(135deg, #FF5370 0%, #FFB64D 100%); border-radius: 16px; width: 48px; height: 48px; display: flex; align-items: center; justify-content: center; box-shadow: 0 4px 12px rgba(255,83,112,0.18);">
                    <i class="feather icon-clock text-white" style="font-size: 1.8rem;"></i>
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <div class="me-3 text-center">
                        <div class="small text-muted mb-1">Online</div>
                        <div style="font-weight: 600; color: #1dc4e9;">'.$rowonlineexpiredusers->result.'</div>
                    </div>
                    <span style="margin: 0 15px;">|</span>
                    <div class="me-3 text-center">
                        <div class="small text-muted mb-1">Offline</div>
                        <div style="font-weight: 600; color: #FF5370;">'.($rowexpireusers->result - $rowonlineexpiredusers->result).'</div>
                    </div>
                </div>
                <a href="admin.php?cont=expired_users" class="btn btn-sm" style="background: linear-gradient(135deg, #FF5370 0%, #FFB64D 100%); color: white; border: none; padding: 8px 16px; border-radius: 8px; font-weight: 500;">
                    <i class="feather icon-list mr-1"></i> View Details
                </a>
            </div>
        </div>
    </div>

  </div>
  <div class="col-xl-8 col-md-6">
      <div class="row mt-4">
          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
              <div id="usageStatsContainer">';
              $output .= $this->usage();
              $output .= '</div>
          </div>
      </div>
  </div>

  <div class="col-xl-4 col-md-4">
      <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 8px 32px rgba(46, 216, 182, 0.1);">
          <div class="card-header py-3" style="background: linear-gradient(90deg, #1dc4e9 0%, #2ed8b6 100%); border-radius: 16px 16px 0 0;">
              <div class="d-flex justify-content-between align-items-center">
                  <h5 class="mb-0 text-white" style="font-weight: 600; font-size: 1.1rem;">
                      <i class="feather icon-box mr-2"></i>Package Statistics
                  </h5>
                
              </div>
          </div>
          <div class="card-body p-4">
              <div class="table-responsive">';
              
              $packageStats = $this->get_package_stats($db, $managerCondition);
              if ($packageStats && $packageStats->num_rows > 0) {
                  $output .= '
                  <table class="table table-hover align-middle mb-0" style="border-radius: 12px; overflow: hidden;">
                      <thead style="background: linear-gradient(90deg, #e3f7fa 0%, #f0f9ff 100%);">
                          <tr>
                              <th style="padding: 16px; color: #1dc4e9; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Package</th>
                              <th style="padding: 16px; text-align: center; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Total</th>
                              <th style="padding: 16px; text-align: center; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Active</th>
                              <th style="padding: 16px; text-align: center; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Online</th>
                              <th style="padding: 16px; text-align: center; color: #333f54; font-weight: 600; border-bottom: 2px solid #e3f7fa;">Expired</th>
                          </tr>
                      </thead>
                      <tbody style="background: #fff;">';
                  
                  while ($row = $packageStats->fetch_assoc()) {
                      $activePercentage = ($row['total_users'] > 0) ? round(($row['active_users'] / $row['total_users']) * 100) : 0;
                      $onlinePercentage = ($row['total_users'] > 0) ? round(($row['online_users'] / $row['total_users']) * 100) : 0;
                      $expiredPercentage = ($row['total_users'] > 0) ? round(($row['expired_users'] / $row['total_users']) * 100) : 0;
                      
                      $statusColor = $activePercentage >= 70 ? '#2ed8b6' : ($activePercentage >= 40 ? '#1dc4e9' : '#ff6b6b');
                      
                      $output .= '
                      <tr style="transition: all 0.3s ease;">
                          <td style="padding: 16px; color: #333f54; font-weight: 500;">
                              <div class="d-flex align-items-center">
                                  <div class="mr-3" style="width: 40px; height: 40px; background: ' . $statusColor . '; border-radius: 10px; display: flex; align-items: center; justify-content: center;">
                                      <i class="feather icon-box text-white" style="font-size: 1.2rem;"></i>
                                  </div>
                                  <div>
                                      <h6 class="mb-0" style="font-size: 0.95rem; color: #333f54;">' . htmlspecialchars($row['package']) . '</h6>
                                      <small class="text-muted" style="font-size: 0.8rem;">' . $row['total_users'] . ' Users</small>
                                  </div>
                              </div>
                          </td>
                          <td style="padding: 16px; text-align: center;">
                              <div class="d-flex flex-column align-items-center">
                                  <span style="font-weight: 600; color: #333f54; font-size: 1.1rem;">' . $row['total_users'] . '</span>
                                  <div class="progress mt-2" style="width: 60px; height: 4px; background-color: #e9ecef; border-radius: 2px;">
                                      <div class="progress-bar" style="width: 100%; background-color: #1dc4e9; border-radius: 2px;"></div>
                                  </div>
                              </div>
                          </td>
                          <td style="padding: 16px; text-align: center;">
                              <div class="d-flex flex-column align-items-center">
                                  <span style="font-weight: 600; color: #2ed8b6; font-size: 1.1rem;">' . $row['active_users'] . '</span>
                                  <div class="progress mt-2" style="width: 60px; height: 4px; background-color: #e9ecef; border-radius: 2px;">
                                      <div class="progress-bar" style="width: ' . $activePercentage . '%; background-color: #2ed8b6; border-radius: 2px;"></div>
                                  </div>
                                  <small class="text-muted" style="font-size: 0.8rem;">' . $activePercentage . '%</small>
                              </div>
                          </td>
                          <td style="padding: 16px; text-align: center;">
                              <div class="d-flex flex-column align-items-center">
                                  <span style="font-weight: 600; color: #1dc4e9; font-size: 1.1rem;">' . $row['online_users'] . '</span>
                                  <div class="progress mt-2" style="width: 60px; height: 4px; background-color: #e9ecef; border-radius: 2px;">
                                      <div class="progress-bar" style="width: ' . $onlinePercentage . '%; background-color: #1dc4e9; border-radius: 2px;"></div>
                                  </div>
                                  <small class="text-muted" style="font-size: 0.8rem;">' . $onlinePercentage . '%</small>
                              </div>
                          </td>
                          <td style="padding: 16px; text-align: center;">
                              <div class="d-flex flex-column align-items-center">
                                  <span style="font-weight: 600; color: #ff6b6b; font-size: 1.1rem;">' . $row['expired_users'] . '</span>
                                  <div class="progress mt-2" style="width: 60px; height: 4px; background-color: #e9ecef; border-radius: 2px;">
                                      <div class="progress-bar" style="width: ' . $expiredPercentage . '%; background-color: #ff6b6b; border-radius: 2px;"></div>
                                  </div>
                                  <small class="text-muted" style="font-size: 0.8rem;">' . $expiredPercentage . '%</small>
                              </div>
                          </td>
                      </tr>';
                  }
                  
                  $output .= '
                      </tbody>
                  </table>';
              } else {
                  $output .= '
                  <div class="text-center py-5">
                      <div class="mb-3">
                          <i class="feather icon-box" style="font-size: 3rem; color: #e3f7fa;"></i>
                      </div>
                      <h6 class="text-muted mb-0" style="font-size: 1rem;">No package statistics available</h6>
                  </div>';
              }
              
              $output .= '
              </div>
          </div>
      </div>
  </div>';

  // Add Network Traffic Card
  // $output .= $this->network_traffic_card();

  $output .= '</div>';


 return $output;
}




private function fetch_query_result($query, $db)
{
    $result = $db->conn->query($query);
    return mysqli_fetch_object($result);
}


private function get_package_stats($db, $managerCondition) {
    // Check if user is admin
    $isAdmin = ($_POST['manager_name'] === "admin");
    
    $query = "SELECT
        s.srvname as package,
        COUNT(DISTINCT u.username) as total_users,
        COUNT(DISTINCT CASE WHEN u.expiration > NOW() THEN u.username END) as active_users,
        COUNT(DISTINCT CASE WHEN ra.acctstoptime IS NULL THEN ra.username END) as online_users,
        COUNT(DISTINCT CASE WHEN u.expiration <= NOW() THEN u.username END) as expired_users
    FROM rm_services s
    LEFT JOIN rm_users u ON s.srvid = u.srvid
    LEFT JOIN radacct ra ON u.username = ra.username AND ra.acctstoptime IS NULL
    WHERE 1=1 ";

    // Add manager condition only if not admin
    if (!$isAdmin) {
        $query .= " AND u.owner = '" . $db->conn->real_escape_string($_POST['manager_name']) . "'";
    }

    $query .= " GROUP BY s.srvid, s.srvname
                HAVING total_users > 0
                ORDER BY total_users DESC";

    return $db->conn->query($query);
}

private function format_package_stats($result) {
    $output = '';
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            // Calculate percentages based on total users
            $active_percentage = $row['total_users'] > 0 ? round(($row['active_users'] / $row['total_users']) * 100, 1) : 0;
            $online_percentage = $row['total_users'] > 0 ? round(($row['online_users'] / $row['total_users']) * 100, 1) : 0;
            
            // Determine status color based on active percentage
            $statusColor = '#2ed8b6'; // Default green
            if ($active_percentage < 30) {
                $statusColor = '#FF5370'; // Red for low usage
            } else if ($active_percentage < 60) {
                $statusColor = '#FFB64D'; // Orange for medium usage
            }
            
            $output .= "
            <tr style='border-bottom: 1px solid #e9ecef;'>
                <td style='padding: 12px;'>
                    <div style='display: flex; align-items: center;'>
                        <div style='width: 40px; height: 40px; background-color: {$statusColor}20; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;'>
                            <i class='feather icon-box' style='color: {$statusColor}; font-size: 18px;'></i>
                        </div>
                        <div>
                            <div style='font-weight: 600; color: #333f54;'>" . htmlspecialchars($row['package']) . "</div>
                            <div style='font-size: 12px; color: #6c757d;'>Total: " . $row['total_users'] . " users</div>
                        </div>
                    </div>
                </td>
                <td style='padding: 12px; text-align: center;'>
                    <div style='display: flex; flex-direction: column; align-items: center;'>
                        <div style='font-weight: 600; color: #333f54;'>{$row['active_users']}</div>
                        <div class='progress mt-1' style='width: 60px; height: 4px; background-color: #e9ecef; border-radius: 2px;'>
                            <div class='progress-bar bg-success' style='width: {$active_percentage}%; border-radius: 2px;'></div>
                        </div>
                        <div style='font-size: 12px; color: #6c757d;'>{$active_percentage}%</div>
                    </div>
                </td>
                <td style='padding: 12px; text-align: center;'>
                    <div style='display: flex; flex-direction: column; align-items: center;'>
                        <div style='font-weight: 600; color: #333f54;'>{$row['online_users']}</div>
                        <div class='progress mt-1' style='width: 60px; height: 4px; background-color: #e9ecef; border-radius: 2px;'>
                            <div class='progress-bar bg-info' style='width: {$online_percentage}%; border-radius: 2px;'></div>
                        </div>
                        <div style='font-size: 12px; color: #6c757d;'>{$online_percentage}%</div>
                    </div>
                </td>
            </tr>";
        }
    } else {
        $output = "<tr><td colspan='3' style='padding: 20px; text-align: center; color: #6c757d;'>No package data available</td></tr>";
    }
    return $output;
}



  
}
?>



<!-- Remove loader div and script -->














