<!-- Data Container Starts -->
<div class="pcoded-main-container">
  <div class="pcoded-wrapper">
      <div class="pcoded-content">
          <div class="pcoded-inner-content">
              <!-- [ breadcrumb ] start -->

              <!-- [ breadcrumb ] end -->
              <div class="main-body">
                  <div class="page-wrapper">
                      <!-- [ Main Content ] start -->
                      <div class="row">
                          <!-- Titles Section Responsive Div Starts -->
                          <div class="col-xl-12 col-lg-12 col-md-12 col-sm-12 col-12">
                              <!-- Page Header Starts -->
<!-- Hidden Manager Section -->
<div id="managerRoleSection" class="d-none">
    {ACTMANAGER} <!-- Placeholder for the manager's role -->
</div>

<div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 8px 32px rgba(46, 216, 182, 0.1);">
    <div class="card-header py-3" style="background: linear-gradient(90deg, #FF5370 0%, #FFB64D 100%); border-radius: 16px 16px 0 0;">
        <div class="row align-items-center">
            <div class="col-md-4">
                <h5 class="mb-0 text-white" style="font-weight: 600; font-size: 1.15rem;">
                    <i class="feather icon-users mr-2"></i>Offline Users
                </h5>
                <span class="text-white-50 mt-1 d-block" style="font-size: 0.85rem; opacity: 0.85;">ALL OFFLINE USERS INFORMATION</span>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center justify-content-center gap-3">
                    <div class="status-indicator active" style="background: rgba(255, 255, 255, 0.1); padding: 0.5rem 1rem; border-radius: 30px; cursor: pointer;" onclick="filterByStatus('active')">
                        <span class="status-dot"></span>
                        <span class="status-text text-white" style="font-weight: 500;">Active</span>
                    </div>
                    <div class="status-indicator expired" style="background: rgba(255, 255, 255, 0.1); padding: 0.5rem 1rem; border-radius: 30px; cursor: pointer;" onclick="filterByStatus('expired')">
                        <span class="status-dot"></span>
                        <span class="status-text text-white" style="font-weight: 500;">Expired</span>
                    </div>
                    <div class="status-indicator all" style="background: rgba(255, 255, 255, 0.1); padding: 0.5rem 1rem; border-radius: 30px; cursor: pointer; display: none;" onclick="filterByStatus('all')">
                        <span class="status-text text-white" style="font-weight: 500;">Show All</span>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex flex-column align-items-end">
                    <span class="text-white-50 mb-1" style="font-size: 0.85rem;">OFFLINE USERS</span>
                    <div class="d-flex align-items-center">
                        <i class="feather icon-user-minus text-white mr-2" style="font-size: 1.5rem;"></i>
                        <h2 id="allusers" class="mb-0 text-white" style="font-size: 2rem; font-weight: 600;">0</h2>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 text-center mt-2">
            <div class="auto-refresh-status" style="font-size: 0.813rem; color: rgba(255, 255, 255, 0.7);">
                <i class="feather icon-clock me-1"></i>
                <span>Auto-refreshes in </span>
                <span id="refreshCounter">30</span>
                <span>s</span>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive" style="max-height: none; overflow: visible;">       
            <table id="offline-users-table" class="table table-hover align-middle mb-0">
                <thead style="position: sticky; top: 0; z-index: 1; background: linear-gradient(90deg, #fff1f1 0%, #fff8e8 100%);">
                    <tr>
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">#</th>
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">User ID</th>
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">Full Name</th>
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">Start Time</th>
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">Stop Time</th>
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">Last Online Session</th> 
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">Data Usage</th>
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">Package</th>
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">Expiration</th>
                    </tr>
                </thead>
                <tbody id="offline_users_list">
                    <!-- Rows will be dynamically loaded here -->
                </tbody>
            </table>
        </div>
    </div>
</div>
</div>
<!-- Add required CSS for styling -->
<style>
.user-status {
    border-radius: 12px;
    display: inline-block;
}
.user-status.active {
    background-color: rgba(46, 216, 182, 0.1);
    color: #2ed8b6;
}
.user-status.expired {
    background-color: rgba(255, 83, 112, 0.1);
    color: #FF5370;
}
.user-status.expiring-soon {
    background-color: rgba(255, 182, 77, 0.1);
    color: #FFB64D;
}

/* Status indicators in header */
.status-indicator .status-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 5px;
}
.status-indicator.active .status-dot {
    background-color: #2ed8b6;
    box-shadow: 0 0 5px rgba(46, 216, 182, 0.5);
}
.status-indicator.expired .status-dot {
    background-color: #FF5370;
    box-shadow: 0 0 5px rgba(255, 83, 112, 0.5);
}

/* Animation for refresh icon */
@keyframes rotating {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}
.rotating {
    animation: rotating 1s linear infinite;
}


.dataTables_paginate .paginate_button:hover {
    background: rgba(29, 196, 233, 0.1) !important;
    color: #1dc4e9 !important;
    transform: translateY(-2px);
}

.dataTables_paginate .paginate_button.current {
    background: linear-gradient(135deg, #FF5370 0%, #FFB64D 100%) !important;
    color: white !important;
    box-shadow: 0 4px 10px rgba(255, 83, 112, 0.2) !important;
    border: none !important;
}

.dataTables_paginate .paginate_button.disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
}

/* DataTables length menu styling */
.dataTables_length {
    margin-bottom: 0;
}

.dataTables_length select {
    min-width: 80px !important;
    height: 38px !important;
    padding: 4px 8px !important;
    font-size: 14px !important;
    border: 1px solid #e2e5e8 !important;
    border-radius: 4px !important;
    background-color: #fff !important;
    margin: 0 8px !important;
}

.dataTables_length label {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 0 !important;
    white-space: nowrap !important;
}

/* Table styling */
.table-responsive {
    border-radius: 12px;
    overflow: hidden;
    max-height: 600px;
    overflow-y: auto !important;
    overflow-x: auto !important;
    position: relative;
}

.table {
    margin-bottom: 0 !important;
    background: #fff;
    border-spacing: 0;
    border-collapse: separate;
}

.table thead th {
    background: linear-gradient(135deg, #fff1f1 0%, #fff8e8 100%);
    color: #333f54;
    font-weight: 600;
    white-space: nowrap;
    border-bottom: 2px solid rgba(255, 83, 112, 0.1);
    font-size: 0.875rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    position: sticky;
    top: 0;
    z-index: 2;
    background: linear-gradient(90deg, #fff1f1 0%, #fff8e8 100%);
}

.table tbody tr {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border-left: none !important;
}

.table tbody tr[data-status="active"] {
    border-left-color: #2ed8b6;
}

.table tbody tr[data-status="expired"] {
    border-left-color: #FF5370;
}

.table tbody tr:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 83, 112, 0.1);
    background: linear-gradient(135deg, rgba(255, 83, 112, 0.05) 0%, rgba(255, 182, 77, 0.05) 100%);
    border-left-width: 6px;
}

/* DataTables search styling */
.dataTables_filter {
    position: relative;
}

.dataTables_filter input {
    width: 240px !important;
    padding: 0.625rem 1rem 0.625rem 2.5rem !important;
    font-size: 0.875rem !important;
    border: 1px solid rgba(255, 83, 112, 0.2) !important;
    border-radius: 12px !important;
    background: linear-gradient(135deg, rgba(255, 83, 112, 0.05) 0%, rgba(255, 182, 77, 0.05) 100%) !important;
}

.dataTables_filter input:focus {
    outline: none !important;
    border-color: #FFB64D !important;
    box-shadow: 0 4px 12px rgba(255, 182, 77, 0.1) !important;
    transform: translateY(-1px);
}

.dataTables_filter label {
    position: relative;
}

.dataTables_filter label:before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23FF5370' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.5;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

/* Refresh button styling */
#refreshTable {
    padding: 0.625rem 1.25rem;
    background: linear-gradient(135deg, #FF5370 0%, #FFB64D 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-weight: 500;
    box-shadow: 0 4px 12px rgba(255, 83, 112, 0.2);
    transition: all 0.3s ease;
}

#refreshTable:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(255, 83, 112, 0.25);
}

#refreshTable i {
    margin-right: 0.5rem;
}

/* Loading overlay enhancement */
.table-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.table-loading-overlay .spinner-border {
    color: #FF5370;
}

/* Sync scrolling between header and body */
.dataTables_scrollBody {
    overflow: visible !important;
}

.table thead {
    position: sticky;
    top: 0;
    z-index: 2;
    background: linear-gradient(90deg, #fff1f1 0%, #fff8e8 100%);
}

.table thead th {
    position: sticky;
    top: 0;
    z-index: 2;
    background: linear-gradient(90deg, #fff1f1 0%, #fff8e8 100%);
    white-space: nowrap;
}

/* Custom scrollbar colors to match theme */
.table-responsive::-webkit-scrollbar {
    height: 6px;
    width: 6px;
}

.table-responsive::-webkit-scrollbar-track {
    background: rgba(255, 83, 112, 0.1);
    border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg, #FF5370 0%, #FFB64D 100%);
    border-radius: 3px;
    border: 2px solid transparent;
    background-clip: padding-box;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(90deg, #FF5370 0%, #FFB64D 100%);
}
</style>

<!-- Load required scripts -->
<script src="assets2/js/common.js"></script>
<script>
// Helper function to format bytes
function formatBytes(bytes, decimals = 2) {
    if (!bytes || bytes === 0) return '0 B';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

// Make filterByStatus available globally
window.filterByStatus = function(status) {
    var table = $('#offline-users-table').DataTable();
    
    // Show/hide the "Show All" button
    $('.status-indicator.all').toggle(status !== 'all');
    
    // Apply visual feedback to status indicators
    $('.status-indicator').css('opacity', '0.5');
    if (status === 'all') {
        $('.status-indicator.active, .status-indicator.expired').css('opacity', '1');
    } else {
        $('.status-indicator.' + status).css('opacity', '1');
    }
    
    // Apply the filter
    if (status === 'all') {
        table.search('').columns().search('').draw();
    } else if (status === 'active') {
        table.column(8).search('Active|day left|days left', true, false).draw();
    } else if (status === 'expired') {
        table.column(8).search('Expired', true, false).draw();
    }
};

$(document).ready(function() {
    // Add loading overlay
    $('body').append('<div id="loading-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:9999;"><div style="position:absolute; top:50%; left:50%; transform:translate(-50%,-50%); color:white; text-align:center;"><i class="fa fa-spinner fa-spin fa-3x"></i><br>Loading...</div></div>');

    // Initialize DataTable with enhanced styling
    var table = $('#offline-users-table').DataTable({
        "processing": true,
        "serverSide": false,
        "pageLength": 100,
        "lengthMenu": [
            [100, 250, 500, -1],
            ['100', '250', '500', 'All']
        ],
        "order": [[4, 'desc']], // Order by stop time
        "dom": "<'row mb-3'<'col-sm-12 col-md-4'l><'col-sm-12 col-md-8'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row mt-3'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "language": {
            "lengthMenu": "_MENU_ entries",
            "info": "<span class='text-muted small'>Showing</span> _START_ <span class='text-muted small'>to</span> _END_ <span class='text-muted small'>of</span> _TOTAL_ <span class='text-muted small'>entries</span>",
            "searchPlaceholder": "Search users, packages...",
            "search": "",
            "zeroRecords": '<div class="text-center py-4">' +
                          '<div class="avatar-lg mx-auto mb-3" style="width: 64px; height: 64px; background: rgba(255, 83, 112, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center;">' +
                          '<i class="fas fa-user-slash fa-2x" style="color: #FF5370;"></i>' +
                          '</div>' +
                          '<h5 class="text-muted font-weight-normal mb-2">No offline users found</h5>' +
                          '<p class="text-muted small mb-0">All users are currently connected to the network</p>' +
                          '</div>'
        },
        "drawCallback": function(settings) {
            // Add animations to rows
            $('tbody tr').each(function(index) {
                $(this)
                    .css('opacity', 0)
                    .css('transform', 'translateY(10px)')
                    .delay(index * 50)
                    .animate({
                        opacity: 1,
                        transform: 'translateY(0)'
                    }, 300);
            });
        },
        "ajax": {
            "url": "admindashboard.php",
            "type": "POST",
            "data": function(d) {
                return {
                    action: "offline_users",
                    manager_name: $("#managerRoleSection").text().trim() || "admin"
                };
            },
            "beforeSend": function() {
                $('#loading-overlay').show();
            },
            "complete": function() {
                $('#loading-overlay').hide();
            },
            "dataSrc": function(json) {
                if (json && json.data) {
                    $('#allusers').text(json.data.length);
                }
                return json.data || [];
            }
        },
        "columns": [
            { 
                "data": null,                "render": function(data, type, row, meta) {
                    var borderColor = row.enableuser == 1 ? '#2ed8b6' : '#FF5370';
                    
                    return '<div class="badge bg-light text-dark" style="min-width: 24px; position: relative; z-index: 1;">' + 
                           (meta.row + meta.settings._iDisplayStart + 1) + 
                           '<div style="position: absolute; left: -15px; top: -12px; bottom: -12px; width: 15px; background: ' + borderColor + ';"></div>' +
                           '</div>';
                }
            },
            { 
                "data": "username",
                "defaultContent": "N/A",
                "render": function(data, type, row) {
                    if (!data) return '<span class="text-muted">N/A</span>';
                    
                    var isExpired = false;
                    if (row.expiration) {
                        var expiryDate = new Date(row.expiration);
                        var today = new Date();
                        isExpired = expiryDate < today;
                    }
                    
                    // Add data-status attribute to the parent row
                    $(row).attr('data-status', isExpired ? 'expired' : 'active');
                    
                    var statusClass = isExpired ? 'expired' : 'active';
                    var avatarGradient = isExpired ? 
                        'linear-gradient(135deg, #FF5370 0%, #FFB64D 100%)' : 
                        'linear-gradient(135deg, #1dc4e9 0%, #2ed8b6 100%)';
                    
                    return '<div class="d-flex align-items-center">' +
                           '<div class="avatar-sm" style="background: ' + avatarGradient + '; width: 32px; height: 32px; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 12px;">' +
                           '<i class="feather icon-user text-white"></i>' +
                           '</div>' +
                           '<div class="ms-2">' +
                           '<div class="d-flex align-items-center">' +
                           '<a href="admin.php?cont=edit_user&username=' + encodeURIComponent(data) + '" class="text-dark" style="font-size: 0.875rem; font-weight: 500; margin-right: 8px;">' + 
                           data + '</a>' +
                           '<span class="user-status ' + statusClass + '" style="font-size: 0.7rem; padding: 0.25rem 0.5rem; min-width: 60px; text-align: center;">' + 
                           (isExpired ? 'Expired' : 'Active') + '</span>' +
                           '</div>' +
                           '</div>' +
                           '</div>';
                }
            },
            { 
                "data": "fullname",
                "defaultContent": "N/A",
                "render": function(data) {
                    return data || '<span class="text-muted">N/A</span>';
                }
            },
            { 
                "data": "acctstarttime",
                "defaultContent": "N/A",
                "render": function(data) {
                    if (!data) return '<span class="text-muted">N/A</span>';
                    
                    try {
                        var date = new Date(data);
                        var formattedDate = date.toLocaleString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                        return formattedDate;
                    } catch (e) {
                        return '<span class="text-muted">Invalid Date</span>';
                    }
                }
            },
            { 
                "data": "acctstoptime",
                "defaultContent": "N/A",
                "render": function(data) {
                    if (!data) return '<span class="text-muted">N/A</span>';
                    
                    try {
                        var date = new Date(data);
                        var formattedDate = date.toLocaleString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            hour: '2-digit',
                            minute: '2-digit'
                        });
                        return formattedDate;
                    } catch (e) {
                        return '<span class="text-muted">Invalid Date</span>';
                    }
                }
            },
            { 
                "data": "sessiontime",
                "defaultContent": "N/A",
                "render": function(data) {
                    return data || '<span class="text-muted">N/A</span>';
                }
            },
            { 
                "data": null,
                "render": function(data, type, row) {
                    var upload = row.acctinputoctets ? formatBytes(row.acctinputoctets) : '0 B';
                    var download = row.acctoutputoctets ? formatBytes(row.acctoutputoctets) : '0 B';
                    
                    return '<div class="d-flex flex-column">' +
                           '<span class="text-muted small"><i class="fas fa-download" style="margin-right: 8px;"></i>' + download + '</span>' +
                           '<span class="text-muted small"><i class="fas fa-upload" style="margin-right: 8px;"></i>' + upload + '</span>' +
                           '</div>';
                }
            },
            { 
                "data": "srvname",
                "defaultContent": "N/A",
                "render": function(data) {
                    return data ? '<span class="text-muted"><i class="fas fa-server" style="margin-right: 8px;"></i>' + data + '</span>' : '<span class="text-muted">N/A</span>';
                }
            },
            { 
                "data": "expiration",
                "defaultContent": "N/A",
                "render": function(data) {
                    if (!data) return '<span class="text-muted">N/A</span>';
                    
                    try {
                        var expiryDate = new Date(data);
                        var today = new Date();
                        var diffDays = Math.ceil((expiryDate - today) / (1000 * 60 * 60 * 24));
                        
                        var formattedDate = expiryDate.toLocaleString('en-US', {
                            month: 'short',
                            day: 'numeric',
                            year: 'numeric'
                        });
                        
                        if (diffDays <= 0) {
                            // Expired
                            return '<div class="d-flex flex-column">' +
                                   '<span class="text-muted"><i class="fas fa-calendar" style="margin-right: 8px;"></i>' + formattedDate + '</span>' +
                                   '<span class="text-danger small"><i class="fas fa-exclamation-circle" style="margin-right: 8px;"></i>' + Math.abs(diffDays) + ' Day' + (Math.abs(diffDays) > 1 ? 's' : '') + ' ago</span>' +
                                   '</div>';
                        } else if (diffDays === 1) {
                            // Expires tomorrow
                            return '<div class="d-flex flex-column">' +
                                   '<span class="text-muted"><i class="fas fa-calendar" style="margin-right: 8px;"></i>' + formattedDate + '</span>' +
                                   '<span class="text-warning small"><i class="fas fa-clock" style="margin-right: 8px;"></i>Expires tomorrow</span>' +
                                   '</div>';
                        } else if (diffDays <= 3) {
                            // Expiring soon
                            return '<div class="d-flex flex-column">' +
                                   '<span class="text-muted"><i class="fas fa-calendar" style="margin-right: 8px;"></i>' + formattedDate + '</span>' +
                                   '<span class="text-warning small"><i class="fas fa-clock" style="margin-right: 8px;"></i>' + diffDays + ' Days left</span>' +
                                   '</div>';
                        } else {
                            // Active
                            return '<div class="d-flex flex-column">' +
                                   '<span class="text-muted"><i class="fas fa-calendar" style="margin-right: 8px;"></i>' + formattedDate + '</span>' +
                                   '<span class="text-success small"><i class="fas fa-check-circle" style="margin-right: 8px;"></i>' + diffDays + ' Days left</span>' +
                                   '</div>';
                        }
                    } catch (e) {
                        return '<span class="text-muted">Invalid Date</span>';
                    }
                }
            }
        ]
    });
    
    // Add refresh button with enhanced styling
    $('.dataTables_filter').append(
        '<button id="refreshTable" class="btn btn-primary btn-sm ms-2" style="background: linear-gradient(135deg, #FF5370 0%, #FFB64D 100%); border: none; box-shadow: 0 4px 12px rgba(255, 83, 112, 0.2);">' +
        '<i class="feather icon-refresh-ccw me-1"></i> Refresh' +
        '</button>'
    );

    // Add refresh functionality
    $('#refreshTable').on('click', function() {
        table.ajax.reload();
    });

    // Add auto-refresh functionality
    var refreshInterval = 30000; // 30 seconds
    var refreshCounter = refreshInterval / 1000;
    var refreshTimer;

    function startRefreshTimer() {
        refreshTimer = setInterval(function() {
            refreshCounter--;
            $('#refreshCounter').text(refreshCounter);
            if (refreshCounter <= 0) {
                table.ajax.reload(null, false);
                refreshCounter = refreshInterval / 1000;
            }
        }, 1000);
    }

    startRefreshTimer();

    // Add filter by status functionality
    window.filterByStatus = function(status) {
        var table = $('#offline-users-table').DataTable();
        
        // Show/hide the "Show All" button
        $('.status-indicator.all').toggle(status !== 'all');
        
        // Apply visual feedback to status indicators
        $('.status-indicator').css('opacity', '0.5');
        if (status === 'all') {
            $('.status-indicator.active, .status-indicator.expired').css('opacity', '1');
        } else {
            $('.status-indicator.' + status).css('opacity', '1');
        }
        
        // Apply the filter
        if (status === 'all') {
            table.search('').columns().search('').draw();
        } else if (status === 'active') {
            table.column(8).search('Day left|Days left', true, false).draw();
        } else if (status === 'expired') {
            table.column(8).search('ago', true, false).draw();
        }
    };
    
    // Add tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Add this new code after table initialization
    $('.table-responsive').on('scroll', function() {
        var scrollLeft = $(this).scrollLeft();
        $(this).find('thead').css('transform', 'translateX(-' + scrollLeft + 'px)');
    });
});
</script>


