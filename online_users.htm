<!-- Data Container Starts -->
<div class="pcoded-main-container">
    <div class="pcoded-wrapper">
        <div class="pcoded-content">
            <div class="pcoded-inner-content">
                <!-- [ breadcrumb ] start -->
  
                <!-- [ breadcrumb ] end -->
                <div class="main-body">
                    <div class="page-wrapper">
                        <!-- [ Main Content ] start -->
                        <div class="row">
                            <!-- Titles Section Responsive Div Starts -->
                            <div class="col-xl-12">
                                <!-- Page Header Starts -->
  <!-- Hidden Manager Section -->
  <div id="managerRoleSection" class="d-none">
      {ACTMANAGER} <!-- Placeholder for the manager's role -->
  </div>
  <div class="card border-0" style="background: rgba(255, 255, 255, 0.9); backdrop-filter: blur(10px); border-radius: 16px; box-shadow: 0 8px 32px rgba(46, 216, 182, 0.1);">
    <div class="card-header py-3" style="background: linear-gradient(90deg, #1dc4e9 0%, #2ed8b6 100%); border-radius: 16px 16px 0 0;">
        <div class="row align-items-center">
            <div class="col-md-4">
                <h5 class="mb-0 text-white" style="font-weight: 600; font-size: 1.15rem;">
                    <i class="feather icon-users mr-2"></i>Online Users
                </h5>
                <span class="text-white-50 mt-1 d-block" style="font-size: 0.85rem; opacity: 0.85;">ALL ONLINE USERS INFORMATION</span>
            </div>
            <div class="col-md-4">
                <div class="d-flex align-items-center justify-content-center gap-3">
                    <div class="status-indicator active" style="background: rgba(255, 255, 255, 0.1); padding: 0.5rem 1rem; border-radius: 30px; cursor: pointer;" onclick="filterByStatus('active')">
                        <span class="status-dot"></span>
                        <span class="status-text text-white" style="font-weight: 500;">Active</span>
                    </div>
                    <div class="status-indicator expired" style="background: rgba(255, 255, 255, 0.1); padding: 0.5rem 1rem; border-radius: 30px; cursor: pointer;" onclick="filterByStatus('expired')">
                        <span class="status-dot"></span>
                        <span class="status-text text-white" style="font-weight: 500;">Expired</span>
                    </div>
                    <div class="status-indicator all" style="background: rgba(255, 255, 255, 0.1); padding: 0.5rem 1rem; border-radius: 30px; cursor: pointer; display: none;" onclick="filterByStatus('all')">
                        <span class="status-text text-white" style="font-weight: 500;">Show All</span>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="d-flex flex-column align-items-end">
                    <span class="text-white-50 mb-1" style="font-size: 0.85rem;">LIVE USERS</span>
                    <div class="d-flex align-items-center">
                        <i class="feather icon-zap text-white mr-2" style="font-size: 1.5rem;"></i>
                        <h2 id="allusers" class="mb-0 text-white" style="font-size: 2rem; font-weight: 600;">0</h2>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body">
        <div class="table-responsive" style="max-height: none; overflow: visible;">       
            <table id="online-users-table" class="table table-hover align-middle mb-0">
                <thead style="position: sticky; top: 0; z-index: 1; background: linear-gradient(90deg, #e3f7fa 0%, #f0f9ff 100%);">
                    <tr>
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">#</th>
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">Username</th>
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">Package</th>
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">Start Time</th>
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">Online Time</th>
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">Upload</th>
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">Download</th>
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">IP Address</th>
                        <th class="border-0" style="padding: 16px; color: #333f54; font-weight: 600;">MAC Address</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Table body content will be dynamically populated -->
                </tbody>
            </table>
        </div>
    </div>
</div>
</div>
<!-- Data Container Ends -->

<script src="assets2/js/common.js"></script>
<script>
$(document).ready(function() {
    // Add loading overlay
    $('body').append('<div id="loading-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:9999;"><div style="position:absolute; top:50%; left:50%; transform:translate(-50%,-50%); color:white; text-align:center;"><i class="fa fa-spinner fa-spin fa-3x"></i><br>Loading...</div></div>');

    // Initialize DataTable with enhanced styling
    var table = $('#online-users-table').DataTable({
        "processing": true,
        "serverSide": false,
        "pageLength": 100,
        "lengthMenu": [
            [100, 250, 500, -1],
            ['100', '250', '500', 'All']
        ],
        "order": [[3, 'desc']],
        "dom": "<'row mb-3'<'col-sm-12 col-md-4'l><'col-sm-12 col-md-8'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row mt-3'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "language": {
            "lengthMenu": "_MENU_ entries per page",
            "info": "<span class='text-muted small'>Showing</span> _START_ <span class='text-muted small'>to</span> _END_ <span class='text-muted small'>of</span> _TOTAL_ <span class='text-muted small'>entries</span>",
            
            "searchPlaceholder": "Search users, IP, MAC...",
            "zeroRecords": '<div class="text-center py-4">' +
                          '<div class="avatar-lg mx-auto mb-3" style="width: 64px; height: 64px; background: rgba(29, 196, 233, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center;">' +
                          '<i class="fas fa-users fa-2x" style="color: #1dc4e9;"></i>' +
                          '</div>' +
                          '<h5 class="text-muted font-weight-normal mb-2">No online users found</h5>' +
                          '<p class="text-muted small mb-0">No users are currently connected to the network</p>' +
                          '</div>'
        },
        "drawCallback": function(settings) {
            // Add animations to rows and sync table header
            var $tableBody = $(this).find('.dataTables_scrollBody');
            var $tableHeader = $(this).find('.dataTables_scrollHead');
            
            // Sync horizontal scroll between header and body
            $tableBody.on('scroll', function() {
                $tableHeader.scrollLeft($(this).scrollLeft());
            });

            // Add border indicators for user status
            $('tbody tr').each(function(index) {
                var $row = $(this);
                var isExpired = $row.find('.user-status').hasClass('expired');
                var borderColor = isExpired ? '#FF5370' : '#2ed8b6';
                
                $row.css({
                    'border-left': '4px solid ' + borderColor,
                    'opacity': 0,
                    'transform': 'translateY(10px)'
                }).delay(index * 50).animate({
                    opacity: 1,
                    transform: 'translateY(0)'
                }, 300);
            });
        },
        "ajax": {
            "url": "admindashboard.php",
            "type": "POST",
            "data": function(d) {
                return {
                    action: "online_users",
                    manager_name: $("#managerRoleSection").text().trim() || "admin"
                };
            },
            "beforeSend": function() {
                $('#loading-overlay').show();
            },
            "complete": function() {
                $('#loading-overlay').hide();
            },
            "dataSrc": function(json) {
                if (json && json.data) {
                    $('#allusers').text(json.data.length);
                }
                return json.data || [];
            }
        },
        "columns": [
            { 
                "data": null, 
                "render": function(data, type, row, meta) { 
                    return '<div class="text-center"><span class="badge bg-light text-dark" style="min-width: 24px; font-size: 0.75rem; padding: 0.15rem 0.4rem;">' + 
                           (meta.row + meta.settings._iDisplayStart + 1) + '</span></div>'; 
                },
                "width": "5%"
            },
            { 
                "data": null,
                "render": function(data, type, row) {
                    var statusClass = row.expired ? 'expired' : 'active';
                    var avatarGradient = row.expired ? 
                        'linear-gradient(135deg, #FF5370 0%, #FFB64D 100%)' : 
                        'linear-gradient(135deg, #1dc4e9 0%, #2ed8b6 100%)';
                    
                    return '<div class="d-flex align-items-center">' +
                           '<div class="avatar-sm" style="background: ' + avatarGradient + ';">' +
                           '<i class="feather icon-user text-white"></i>' +
                           '</div>' +
                           '<div class="ms-3">' +
                           '<div class="d-flex align-items-center">' +
                           '<a href="admin.php?cont=edit_user&username=' + encodeURIComponent(row.username) + '" class="text-dark" style="font-size: 0.875rem; font-weight: 500; margin-right: 8px;">' + 
                           row.username + '</a>' +
                           '<span class="user-status ' + statusClass + '" style="font-size: 0.7rem; padding: 0.25rem 0.5rem; min-width: 60px; text-align: center;">' + 
                           (row.expired ? 'Expired' : 'Active') + '</span>' +
                           '</div>' +
                           '</div>' +
                           '</div>';
                },
                "width": "20%"
            },
            { 
                "data": "srvname",
                "render": function(data) {
                    return '<div class="d-flex align-items-center">' +
                           '<i class="feather icon-package me-2"></i>' +
                           '<span class="package-name">' + data + '</span>' +
                           '</div>';
                },
                "width": "15%"
            },
            { 
                "data": "acctstarttime",
                "render": function(data) {
                    var date = new Date(data);
                    var formattedDate = date.toLocaleString('en-US', {
                        month: 'short',
                        day: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                    });
                    return '<div class="d-flex align-items-center">' +
                           '<i class="feather icon-clock me-2"></i>' +
                           '<span class="text-muted" style="font-size: 0.8rem; background-color: rgba(108, 117, 125, 0.08); padding: 0.15rem 0.5rem; border-radius: 10px;">' + 
                           formattedDate + '</span>' +
                           '</div>';
                },
                "width": "15%"
            },
            { 
                "data": "elapsedTime",
                "render": function(data) {
                    return '<div class="d-flex align-items-center">' +
                           '<i class="feather icon-watch me-2"></i>' +
                           '<span class="elapsed-time">' + data + '</span>' +
                           '</div>';
                },
                "width": "10%"
            },
            { 
                "data": "uploadedData",
                "render": function(data) {
                    return '<div class="d-flex align-items-center">' +
                           '<i class="feather icon-upload me-2"></i>' +
                           '<span class="upload-data">' + data + '</span>' +
                           '</div>';
                },
                "width": "10%"
            },
            { 
                "data": "downloadedData",
                "render": function(data) {
                    return '<div class="d-flex align-items-center">' +
                           '<i class="feather icon-download me-2"></i>' +
                           '<span class="download-data">' + data + '</span>' +
                           '</div>';
                },
                "width": "10%"
            },
            { 
                "data": "framedipaddress",
                "render": function(data) {
                    return '<div class="d-flex align-items-center">' +
                           '<i class="feather icon-wifi me-2"></i>' +
                           '<span class="ip-address">' + data + '</span>' +
                           '</div>';
                },
                "width": "10%"
            },
            { 
                "data": "callingstationid",
                "render": function(data) {
                    return '<div class="d-flex align-items-center">' +
                           '<i class="feather icon-hash me-2"></i>' +
                           '<span class="mac-address">' + data + '</span>' +
                           '</div>';
                },
                "width": "10%"
            }
        ],
        "dom": "<'row mb-3'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row mt-3'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "language": {
            "lengthMenu": "<span class='text-muted'>Show</span> _MENU_ <span class='text-muted'>entries</span>",
            "zeroRecords": '<div class="text-center py-5">' +
                          '<div class="avatar-lg mx-auto mb-3" style="width: 64px; height: 64px; background: rgba(29, 196, 233, 0.1); border-radius: 50%; display: flex; align-items: center; justify-content: center;">' +
                          '<i class="fas fa-users fa-2x" style="color: #1dc4e9;"></i>' +
                          '</div>' +
                          '<h5 class="text-muted font-weight-normal">No online users found</h5>' +
                          '<p class="text-muted small mb-0">No users are currently connected to the network</p>' +
                          '</div>',
            "info": "<span class='text-muted small'>Showing</span> _START_ <span class='text-muted small'>to</span> _END_ <span class='text-muted small'>of</span> _TOTAL_ <span class='text-muted small'>entries</span>",
            "infoEmpty": "<span class='text-muted small'>Showing 0 entries</span>",
            "infoFiltered": "<span class='text-muted small'>(filtered from _MAX_ total entries)</span>",
            "search": "<i class hidden='fas fa-search text-muted'></i>",
            "searchPlaceholder": "Search users, IP, MAC...",
            "paginate": {
                "first": '<i class="fas fa-angle-double-left"></i>',
                "last": '<i class="fas fa-angle-double-right"></i>',
                "next": '<i class="fas fa-angle-right"></i>',
                "previous": '<i class="fas fa-angle-left"></i>'
            },
            "processing": '<div class="text-center py-3">' +
                         '<div class="spinner-border text-primary" role="status">' +
                         '<span class="sr-only">Loading...</span>' +
                         '</div>' +
                         '<div class="text-muted small mt-2">Loading user data...</div>' +
                         '</div>'
        },
        "initComplete": function(settings, json) {
            // Add gradient background to the search input
            $('.dataTables_filter input')
                .addClass('py-2 px-4')
                .css({
                    'background': 'linear-gradient(135deg, rgba(227, 247, 250, 0.1) 0%, rgba(240, 249, 255, 0.1) 100%)',
                    'border': '1px solid rgba(29, 196, 233, 0.2)',
                    'box-shadow': '0 2px 6px rgba(29, 196, 233, 0.05)',
                    'width': '300px'
                });

            // Style the length select
            $('.dataTables_length select')
                .addClass('custom-select-sm')
                .css({
                    'background': 'linear-gradient(135deg, rgba(227, 247, 250, 0.1) 0%, rgba(240, 249, 255, 0.1) 100%)',
                    'border': '1px solid rgba(29, 196, 233, 0.2)',
                    'box-shadow': '0 2px 6px rgba(29, 196, 233, 0.05)',
                    'padding': '0.4rem 2rem 0.4rem 1rem',
                    'border-radius': '8px'
                });
        },
        "drawCallback": function(settings) {
            var api = this.api();
            var pageInfo = api.page.info();
            
            // Add animations to new rows
            $('tbody tr').each(function(index) {
                $(this)
                    .css('opacity', 0)
                    .css('transform', 'translateY(10px)')
                    .delay(index * 50)
                    .animate({
                        opacity: 1,
                        transform: 'translateY(0)'
                    }, 300);
            });
            
            // Update pagination buttons
            if (pageInfo.recordsTotal <= 100) {
                $('.dataTables_paginate').hide();
            } else {
                $('.dataTables_paginate').show();
                
                // Style the current page button
                $('.paginate_button.current').css({
                    'background': 'linear-gradient(135deg, #1dc4e9 0%, #2ed8b6 100%)',
                    'color': '#fff',
                    'border': 'none',
                    'box-shadow': '0 4px 12px rgba(29, 196, 233, 0.2)'
                });
            }
            
            // Add tooltips to actions
            $('[data-toggle="tooltip"]').tooltip();
        }
    });
    
    // Add refresh button with enhanced styling
    $('.dataTables_filter').append(
        '<button id="refreshTable" class="btn ms-3" style="padding: 0.625rem 1.25rem; background: linear-gradient(135deg, #1dc4e9 0%, #2ed8b6 100%); border: none; border-radius: 12px; color: white; font-weight: 500; box-shadow: 0 4px 12px rgba(46, 216, 182, 0.2);">' +
        '<i class="feather icon-refresh-cw me-2"></i>' +
        '<span>Refresh</span>' +
        '</button>'
    );
    
    // Add auto-refresh indicator
    $('.card-header .row').append(
        '<div class="col-12 text-center mt-2">' +
        '<div class="auto-refresh-status" style="font-size: 0.813rem; color: rgba(255, 255, 255, 0.7);">' +
        '<i class="feather icon-clock me-1"></i>' +
        '<span>Auto-refreshes in </span>' +
        '<span id="refreshCounter">30</span>' +
        '<span>s</span>' +
        '</div>' +
        '</div>'
    );
    
    // Refresh button click handler with animation
    $('#refreshTable').on('click', function() {
        var $button = $(this);
        var $icon = $button.find('i');
        
        // Disable button and add spinning animation
        $button.prop('disabled', true);
        $icon.addClass('rotating');
        
        // Add loading overlay to table
        $('.table-responsive').append(
            '<div class="table-loading-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background: rgba(255, 255, 255, 0.8); backdrop-filter: blur(4px); display: flex; align-items: center; justify-content: center; z-index: 1000;">' +
            '<div class="loading-content text-center">' +
            '<div class="spinner-border text-primary mb-2" role="status"></div>' +
            '<div class="text-muted small">Refreshing data...</div>' +
            '</div>' +
            '</div>'
        );
        
        table.ajax.reload(function() {
            setTimeout(function() {
                $button.prop('disabled', false);
                $icon.removeClass('rotating');
                $('.table-loading-overlay').fadeOut(300, function() {
                    $(this).remove();
                });
            }, 500);
        }, false);
    });
    
    // Auto refresh counter
    var counter = 30;
    var refreshInterval = setInterval(function() {
        counter--;
        $('#refreshCounter').text(counter);
        
        if (counter <= 0) {
            counter = 30;
            if (!$('#refreshTable').prop('disabled')) {
                table.ajax.reload(null, false);
            }
        }
    }, 1000);
    
    // Add rotation animation
    $('<style>')
        .text('@keyframes rotate360 { from { transform: rotate(0deg); } to { transform: rotate(360deg); } }' +
              '.rotating { animation: rotate360 1s linear infinite; }')
        .appendTo('head');
    
    // Add status filtering function
    window.filterByStatus = function(status) {
        // Show/hide the "Show All" button
        $('.status-indicator.all').toggle(status !== 'all');
        
        // Apply visual feedback to status indicators
        $('.status-indicator').css('opacity', '0.5');
        if (status === 'all') {
            $('.status-indicator.active, .status-indicator.expired').css('opacity', '1');
        } else {
            $('.status-indicator.' + status).css('opacity', '1');
        }
        
        // Apply the filter
        $.fn.dataTable.ext.search.pop(); // Remove any existing filter
        
        if (status !== 'all') {
            $.fn.dataTable.ext.search.push(
                function(settings, data, dataIndex) {
                    var rowData = table.row(dataIndex).data();
                    return status === 'active' ? !rowData.expired : rowData.expired;
                }
            );
        }
        
        // Redraw the table
        table.draw();
        
        // Update the table info
        var filteredCount = table.page.info().recordsDisplay;
        $('#allusers').text(filteredCount);
    };

    // Initialize all filters as active
    window.filterByStatus('all');
});
</script>

<style>
.card {
    border-radius: 16px;
    overflow: hidden;
    animation: slideIn 0.5s ease-out;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(29, 196, 233, 0.15);
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.table > :not(caption) > * > * {
    padding: 1rem;
}

.table tbody tr {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 4px solid transparent;
}

.table tbody tr:hover {
    border-left-width: 6px;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(29, 196, 233, 0.1);
    background: linear-gradient(135deg, rgba(29, 196, 233, 0.05) 0%, rgba(46, 216, 182, 0.05) 100%);
}

.table {
    margin-bottom: 0;
    background: #fff;
    border-radius: 12px;
    border-spacing: 0;
    border-collapse: separate;
}

.table thead th {
    background: linear-gradient(135deg, #e3f7fa 0%, #f0f9ff 100%);
    color: #333f54;
    font-weight: 600;
    padding: 1rem 1.5rem;
    white-space: nowrap;
    border-bottom: 2px solid rgba(29, 196, 233, 0.1);
    font-size: 0.875rem;
    letter-spacing: 0.5px;
    text-transform: uppercase;
    transition: all 0.3s ease;
    position: sticky;
    top: 0;
    z-index: 1;
}

.table thead th:first-child {
    border-top-left-radius: 12px;
}

.table thead th:last-child {
    border-top-right-radius: 12px;
}

.table td {
    padding: 1rem 1.5rem;
    vertical-align: middle;
    border-bottom: 1px solid rgba(29, 196, 233, 0.1);
    font-size: 0.875rem;
    color: #333f54;
}

/* Avatar and User Info */
.avatar-sm {
    width: 42px;
    height: 42px;
    border-radius: 12px;
    background: linear-gradient(135deg, #1dc4e9 0%, #2ed8b6 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    transition: all 0.3s ease;
}

.avatar-sm:hover {
    transform: scale(1.1);
    box-shadow: 0 4px 12px rgba(29, 196, 233, 0.2);
}

.avatar-sm i {
    color: #fff;
    font-size: 1.25rem;
}

/* Status Badges */
.user-status {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
    font-size: 0.7rem;
    text-align: center;
    min-width: 60px;
    line-height: 1;
}

.user-status.active {
    background: rgba(46, 216, 182, 0.1);
    color: #2ed8b6;
    border: 1px solid rgba(46, 216, 182, 0.2);
}

.user-status.expired {
    background: rgba(255, 83, 112, 0.1);
    color: #FF5370;
    border: 1px solid rgba(255, 83, 112, 0.2);
}

/* Ensure proper alignment in the username row */
.d-flex.align-items-center .text-dark {
    margin-right: 8px !important;
}

/* Data Metrics */
.data-metric {
    display: inline-flex;
    align-items: center;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-size: 0.813rem;
    transition: all 0.3s ease;
}

.data-metric i {
    margin-right: 0.5rem;
    font-size: 1rem;
}

.data-metric.download {
    color: #1dc4e9;
    background: rgba(29, 196, 233, 0.1);
    border: 1px solid rgba(29, 196, 233, 0.2);
}

.data-metric.upload {
    color: #2ed8b6;
    background: rgba(46, 216, 182, 0.1);
    border: 1px solid rgba(46, 216, 182, 0.2);
}

.data-metric:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(29, 196, 233, 0.15);
}

/* Package Badge */
.package-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    background: rgba(46, 216, 182, 0.1);
    color: #2ed8b6;
    font-weight: 500;
    font-size: 0.813rem;
    transition: all 0.3s ease;
}

.package-badge i {
    margin-right: 0.5rem;
}

.package-badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(46, 216, 182, 0.15);
}

/* Custom scrollbar */
.table-responsive {
    max-height: 600px;
    overflow-y: auto !important;
    overflow-x: auto !important;
    position: relative;
}

/* Style scrollbars */
.table-responsive::-webkit-scrollbar {
    height: 6px;
    width: 6px;
    display: block !important;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #1dc4e9;
    border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #2ed8b6;
}

/* Ensure table takes full height */
.table {
    margin-bottom: 0;
    width: 100%;
}

/* Adjust card body padding */
.card-body {
    padding: 1.5rem;
    overflow: visible;
}

/* Make table header sticky */
.table thead {
    position: sticky;
    top: 0;
    z-index: 1;
    background: linear-gradient(90deg, #e3f7fa 0%, #f0f9ff 100%);
}

/* Ensure table rows are properly sized */
.table tbody tr {
    height: 60px;
}

/* Add subtle border to separate rows */
.table tbody tr:not(:last-child) {
    border-bottom: 1px solid rgba(29, 196, 233, 0.1);
}

/* Remove DataTables scrollbar */
.dataTables_scrollBody::-webkit-scrollbar {
    display: none !important;
}

.status-indicator {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    border-radius: 20px;
    margin-right: 10px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
}

.status-indicator:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

.status-indicator::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 30px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.status-indicator:hover::before {
    opacity: 1;
}

.status-indicator[style*="opacity: 0.5"] {
    background: rgba(255, 255, 255, 0.05) !important;
}

/* Live Users Counter */
#allusers {
    font-size: 2.5rem;
    font-weight: 600;
    color: #333f54;
    margin: 0;
    line-height: 1;
    animation: countUp 0.5s ease-out;
}

/* Enhanced Pagination */
.dataTables_paginate {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    gap: 0.5rem;
    margin-top: 1.5rem;
}

.dataTables_paginate .paginate_button {
    min-width: 36px;
    height: 36px;
    padding: 0;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.875rem;
    color: #333f54;
    background: transparent;
    border: 1px solid rgba(29, 196, 233, 0.2);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.dataTables_paginate .paginate_button:hover:not(.disabled):not(.current) {
    background: linear-gradient(135deg, rgba(29, 196, 233, 0.1) 0%, rgba(46, 216, 182, 0.1) 100%);
    border-color: #1dc4e9;
    color: #1dc4e9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(29, 196, 233, 0.15);
}

.dataTables_paginate .paginate_button.current {
    background: linear-gradient(135deg, #1dc4e9 0%, #2ed8b6 100%);
    border: none;
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 12px rgba(46, 216, 182, 0.2);
}

.dataTables_paginate .paginate_button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Search Input Enhancement */
.dataTables_filter {
    display: flex;
    align-items: center;
}

.dataTables_filter input {
    border: 1px solid rgba(29, 196, 233, 0.2);
    border-radius: 12px;
    padding: 0.625rem 1rem 0.625rem 2.5rem;
    font-size: 0.875rem;
    color: #333f54;
    background: linear-gradient(135deg, rgba(29, 196, 233, 0.05) 0%, rgba(46, 216, 182, 0.05) 100%);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    width: 240px;
}

.dataTables_filter label {
    position: relative;
    margin: 0;
}

.dataTables_filter label:before {
    content: '';
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    width: 16px;
    height: 16px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%231dc4e9' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Ccircle cx='11' cy='11' r='8'%3E%3C/circle%3E%3Cline x1='21' y1='21' x2='16.65' y2='16.65'%3E%3C/line%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0.5;
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.dataTables_filter input:focus {
    outline: none;
    border-color: #2ed8b6;
    box-shadow: 0 4px 12px rgba(46, 216, 182, 0.1);
    transform: translateY(-1px);
}

.dataTables_filter input:focus + label:before {
    opacity: 1;
}

/* Loading Overlay Enhancement */
.table-loading-overlay {
    animation: fadeIn 0.3s ease;
}

.table-loading-overlay .spinner-border {
    width: 2.5rem;
    height: 2.5rem;
    border-width: 0.25rem;
    border-color: rgba(29, 196, 233, 0.2);
    border-right-color: #1dc4e9;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* Info Text Styling */
.dataTables_info {
    color: #6c757d;
    font-size: 0.875rem;
}

/* Compact styling for data columns */
.package-name, .elapsed-time, .upload-data, .download-data, .ip-address, .mac-address {
    font-size: 0.8rem;
    font-weight: 500;
    padding: 0.15rem 0.5rem;
    border-radius: 10px;
    display: inline-block;
    line-height: 1.2;
}

/* Background colors for data badges */
.package-name {
    color: #2ed8b6;
    background-color: rgba(46, 216, 182, 0.08);
}

.elapsed-time {
    color: #FFB64D;
    background-color: rgba(255, 182, 77, 0.08);
}

.upload-data {
    color: #4CAF50;
    background-color: rgba(76, 175, 80, 0.08);
}

.download-data {
    color: #2196F3;
    background-color: rgba(33, 150, 243, 0.08);
}

.ip-address {
    color: #1dc4e9;
    background-color: rgba(29, 196, 233, 0.08);
}

.mac-address {
    color: #FF5370;
    background-color: rgba(255, 83, 112, 0.08);
}

/* Smaller icon styling */
.feather {
    stroke-width: 2px;
    font-size: 0.9rem;
}

/* Reduced row height */
#online-users-table tbody td {
    padding: 10px 15px;
    vertical-align: middle;
}

/* Ensure consistent vertical alignment with reduced height */
.d-flex.align-items-center {
    height: auto;
    min-height: 32px;
}

.dataTables_length select {
    min-width: 80px !important;
    height: 38px !important;
    padding: 4px 8px !important;
    font-size: 14px !important;
    border: 1px solid #e2e5e8 !important;
    border-radius: 4px !important;
    background-color: #fff !important;
    margin: 0 8px !important;
}

.dataTables_length label {
    display: flex !important;
    align-items: center !important;
    margin-bottom: 0 !important;
    white-space: nowrap !important;
}
</style>

